<?php
/**
 * 股票数据API接口
 * 提供实时股票指数数据
 */

require_once '../config/database.php';
require_once '../utils/response.php';
require_once '../utils/auth.php';

class StockDataAPI {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * 获取股票指数数据
     */
    public function getStockIndices() {
        try {
            // 模拟实时股票数据
            $baseData = [
                [
                    'code' => '000001',
                    'name' => '上证指数',
                    'market' => 'SH',
                    'baseValue' => 3348.37,
                    'baseChange' => -31.82,
                    'basePercent' => -0.94
                ],
                [
                    'code' => '399001',
                    'name' => '深证成指',
                    'market' => 'SZ',
                    'baseValue' => 10132.41,
                    'baseChange' => -87.21,
                    'basePercent' => -0.85
                ],
                [
                    'code' => '399006',
                    'name' => '创业板指',
                    'market' => 'SZ',
                    'baseValue' => 2021.5,
                    'baseChange' => -24.07,
                    'basePercent' => -1.18
                ]
            ];
            
            $stockData = [];
            
            foreach ($baseData as $stock) {
                // 添加随机波动
                $volatility = 0.01; // 1%的波动
                $randomFactor = (rand(-100, 100) / 10000) * $volatility;
                
                $currentValue = $stock['baseValue'] * (1 + $randomFactor);
                $change = $currentValue - $stock['baseValue'];
                $percent = ($change / $stock['baseValue']) * 100;
                
                $stockData[] = [
                    'code' => $stock['code'],
                    'name' => $stock['name'],
                    'market' => $stock['market'],
                    'value' => round($currentValue, 2),
                    'change' => round($change, 2),
                    'percent' => round($percent, 2),
                    'volume' => rand(1000000, 9999999),
                    'turnover' => rand(10000000000, 99999999999),
                    'high' => round($currentValue * 1.02, 2),
                    'low' => round($currentValue * 0.98, 2),
                    'open' => round($currentValue * 0.995, 2),
                    'preClose' => $stock['baseValue'],
                    'timestamp' => time(),
                    'updateTime' => date('Y-m-d H:i:s')
                ];
            }
            
            Response::success($stockData);
            
        } catch (Exception $e) {
            Response::error('获取股票数据失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 获取单个股票详情
     */
    public function getStockDetail($code) {
        try {
            if (empty($code)) {
                Response::error('股票代码不能为空');
                return;
            }
            
            // 验证股票代码格式
            if (!preg_match('/^[0-9]{6}$/', $code)) {
                Response::error('股票代码格式错误');
                return;
            }
            
            // 模拟股票详情数据
            $stockDetail = [
                'code' => $code,
                'name' => $this->getStockName($code),
                'market' => substr($code, 0, 1) === '6' ? 'SH' : 'SZ',
                'value' => rand(1000, 5000) / 100,
                'change' => rand(-500, 500) / 100,
                'percent' => rand(-1000, 1000) / 100,
                'volume' => rand(1000000, 50000000),
                'turnover' => rand(1000000000, 50000000000),
                'marketCap' => rand(10000000000, 500000000000),
                'pe' => rand(500, 5000) / 100,
                'pb' => rand(100, 1000) / 100,
                'high' => rand(1000, 5000) / 100,
                'low' => rand(1000, 5000) / 100,
                'open' => rand(1000, 5000) / 100,
                'preClose' => rand(1000, 5000) / 100,
                'timestamp' => time(),
                'updateTime' => date('Y-m-d H:i:s')
            ];
            
            Response::success($stockDetail);
            
        } catch (Exception $e) {
            Response::error('获取股票详情失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 获取股票历史数据
     */
    public function getStockHistory($code, $period = '1d', $count = 30) {
        try {
            if (empty($code)) {
                Response::error('股票代码不能为空');
                return;
            }
            
            $historyData = [];
            $basePrice = rand(1000, 5000) / 100;
            
            for ($i = $count - 1; $i >= 0; $i--) {
                $date = date('Y-m-d', strtotime("-{$i} days"));
                $volatility = rand(-300, 300) / 10000; // 3%波动
                
                $open = $basePrice * (1 + $volatility);
                $close = $open * (1 + rand(-200, 200) / 10000);
                $high = max($open, $close) * (1 + rand(0, 100) / 10000);
                $low = min($open, $close) * (1 - rand(0, 100) / 10000);
                
                $historyData[] = [
                    'date' => $date,
                    'open' => round($open, 2),
                    'high' => round($high, 2),
                    'low' => round($low, 2),
                    'close' => round($close, 2),
                    'volume' => rand(1000000, 50000000),
                    'turnover' => rand(1000000000, 50000000000)
                ];
                
                $basePrice = $close; // 下一天的基准价格
            }
            
            Response::success([
                'code' => $code,
                'period' => $period,
                'count' => $count,
                'data' => $historyData
            ]);
            
        } catch (Exception $e) {
            Response::error('获取历史数据失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 获取市场概况
     */
    public function getMarketOverview() {
        try {
            $overview = [
                'totalStocks' => rand(4000, 5000),
                'upStocks' => rand(1000, 2000),
                'downStocks' => rand(1500, 2500),
                'flatStocks' => rand(200, 500),
                'totalVolume' => rand(100000000000, 500000000000),
                'totalTurnover' => rand(1000000000000, 5000000000000),
                'averagePrice' => rand(1000, 3000) / 100,
                'marketSentiment' => rand(0, 100),
                'timestamp' => time(),
                'updateTime' => date('Y-m-d H:i:s')
            ];
            
            Response::success($overview);
            
        } catch (Exception $e) {
            Response::error('获取市场概况失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 获取热门股票
     */
    public function getHotStocks($limit = 10) {
        try {
            $hotStocks = [];
            
            for ($i = 0; $i < $limit; $i++) {
                $code = str_pad(rand(1, 999999), 6, '0', STR_PAD_LEFT);
                $hotStocks[] = [
                    'code' => $code,
                    'name' => $this->getStockName($code),
                    'value' => rand(1000, 5000) / 100,
                    'change' => rand(-500, 500) / 100,
                    'percent' => rand(-1000, 1000) / 100,
                    'volume' => rand(1000000, 50000000),
                    'heat' => rand(70, 100)
                ];
            }
            
            // 按热度排序
            usort($hotStocks, function($a, $b) {
                return $b['heat'] - $a['heat'];
            });
            
            Response::success($hotStocks);
            
        } catch (Exception $e) {
            Response::error('获取热门股票失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 根据股票代码生成股票名称
     */
    private function getStockName($code) {
        $names = [
            '平安银行', '万科A', '国农科技', '世纪星源', '深振业A',
            '招商银行', '浦发银行', '民生银行', '兴业银行', '中信银行',
            '贵州茅台', '五粮液', '泸州老窖', '剑南春', '水井坊',
            '中国平安', '中国人寿', '新华保险', '中国太保', '中国人保',
            '腾讯控股', '阿里巴巴', '百度', '京东', '网易'
        ];
        
        return $names[hexdec(substr(md5($code), 0, 2)) % count($names)];
    }
}

// 处理请求
$api = new StockDataAPI();

$action = $_GET['action'] ?? 'indices';

switch ($action) {
    case 'indices':
        $api->getStockIndices();
        break;
    case 'detail':
        $code = $_GET['code'] ?? '';
        $api->getStockDetail($code);
        break;
    case 'history':
        $code = $_GET['code'] ?? '';
        $period = $_GET['period'] ?? '1d';
        $count = intval($_GET['count'] ?? 30);
        $api->getStockHistory($code, $period, $count);
        break;
    case 'overview':
        $api->getMarketOverview();
        break;
    case 'hot':
        $limit = intval($_GET['limit'] ?? 10);
        $api->getHotStocks($limit);
        break;
    default:
        Response::error('无效的操作');
}
?>
