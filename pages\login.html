<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录 - 国信证券</title>
    <link rel="stylesheet" href="../public/css/main.css">
    <link rel="stylesheet" href="../public/css/login.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="login-container">
        <button class="back-btn" onclick="window.location.href='../home/<USER>'">
            <i class="fas fa-arrow-left"></i>
        </button>

        <div class="login-box">
            <div class="login-header">
                <div class="login-logo">
                    <i class="fas fa-chart-line"></i>
                </div>
                <h1 class="login-title">欢迎回来</h1>
                <p class="login-subtitle">登录您的国信证券账户</p>
            </div>

            <form id="loginForm">
                <div class="form-group">
                    <label class="form-label">账号</label>
                    <input type="text" class="form-input" id="username" placeholder="请输入账号" autocomplete="username">
                    <div class="error-message" id="usernameError">请输入有效的账号</div>
                </div>

                <div class="form-group">
                    <label class="form-label">密码</label>
                    <div class="password-input-wrapper">
                        <input type="password" class="form-input" id="password" placeholder="请输入密码" autocomplete="current-password">
                        <button type="button" class="password-toggle" id="passwordToggle">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                    <div class="error-message" id="passwordError">密码不能为空</div>
                </div>

                <div class="login-options">
                    <label class="remember-me">
                        <input type="checkbox" id="rememberMe">
                        <span class="checkmark"></span>
                        <span>记住我</span>
                    </label>
                </div>

                <button type="submit" class="login-btn" id="loginBtn">
                    <span class="btn-text">登录</span>
                    <div class="btn-loading hidden">
                        <i class="fas fa-spinner fa-spin"></i>
                        <span>登录中...</span>
                    </div>
                </button>
            </form>



            <div class="register-link">
                还没有账户？<a href="register.html">立即注册</a>
            </div>
        </div>
    </div>

    <!-- Toast 容器 -->
    <div id="toastContainer"></div>

    <!-- 加载脚本 -->
    <script src="../public/js/utils/constants.js"></script>
    <script src="../public/js/utils/helpers.js"></script>
    <script src="../public/js/login.js"></script>
</body>
</html>
