<?php
/**
 * API响应工具类
 * 统一处理API响应格式
 */

class Response {
    
    /**
     * 发送成功响应
     */
    public static function success($data = null, $message = '操作成功', $code = 200) {
        self::sendResponse(true, $data, $message, $code);
    }
    
    /**
     * 发送错误响应
     */
    public static function error($message = '操作失败', $code = 400, $data = null) {
        self::sendResponse(false, $data, $message, $code);
    }
    
    /**
     * 发送未授权响应
     */
    public static function unauthorized($message = '未授权访问') {
        self::sendResponse(false, null, $message, 401);
    }
    
    /**
     * 发送禁止访问响应
     */
    public static function forbidden($message = '禁止访问') {
        self::sendResponse(false, null, $message, 403);
    }
    
    /**
     * 发送未找到响应
     */
    public static function notFound($message = '资源未找到') {
        self::sendResponse(false, null, $message, 404);
    }
    
    /**
     * 发送服务器错误响应
     */
    public static function serverError($message = '服务器内部错误') {
        self::sendResponse(false, null, $message, 500);
    }
    
    /**
     * 发送验证错误响应
     */
    public static function validationError($errors, $message = '数据验证失败') {
        self::sendResponse(false, ['errors' => $errors], $message, 422);
    }
    
    /**
     * 发送分页数据响应
     */
    public static function paginated($data, $total, $page, $limit, $message = '获取成功') {
        $responseData = [
            'items' => $data,
            'pagination' => [
                'total' => $total,
                'page' => $page,
                'limit' => $limit,
                'pages' => ceil($total / $limit),
                'hasNext' => ($page * $limit) < $total,
                'hasPrev' => $page > 1
            ]
        ];
        
        self::sendResponse(true, $responseData, $message, 200);
    }
    
    /**
     * 发送响应
     */
    private static function sendResponse($success, $data, $message, $httpCode) {
        // 设置HTTP状态码
        http_response_code($httpCode);
        
        // 设置响应头
        header('Content-Type: application/json; charset=utf-8');
        header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
        header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
        
        // 构建响应数据
        $response = [
            'success' => $success,
            'message' => $message,
            'code' => $httpCode,
            'timestamp' => time(),
            'datetime' => date('Y-m-d H:i:s')
        ];
        
        // 添加数据（如果有）
        if ($data !== null) {
            $response['data'] = $data;
        }
        
        // 在调试模式下添加额外信息
        if (defined('DEBUG') && DEBUG) {
            $response['debug'] = [
                'memory_usage' => memory_get_usage(true),
                'execution_time' => microtime(true) - $_SERVER['REQUEST_TIME_FLOAT'],
                'request_method' => $_SERVER['REQUEST_METHOD'],
                'request_uri' => $_SERVER['REQUEST_URI']
            ];
        }
        
        // 输出JSON响应
        echo json_encode($response, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
        
        // 记录API调用日志
        self::logApiCall($success, $httpCode, $message);
        
        exit;
    }
    
    /**
     * 记录API调用日志
     */
    private static function logApiCall($success, $httpCode, $message) {
        try {
            $logData = [
                'timestamp' => date('Y-m-d H:i:s'),
                'success' => $success,
                'code' => $httpCode,
                'message' => $message,
                'method' => $_SERVER['REQUEST_METHOD'],
                'uri' => $_SERVER['REQUEST_URI'],
                'ip' => self::getClientIP(),
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
            ];
            
            // 写入日志文件
            $logFile = __DIR__ . '/../logs/api_' . date('Y-m-d') . '.log';
            $logDir = dirname($logFile);
            
            if (!is_dir($logDir)) {
                mkdir($logDir, 0755, true);
            }
            
            file_put_contents($logFile, json_encode($logData) . "\n", FILE_APPEND | LOCK_EX);
            
        } catch (Exception $e) {
            // 日志记录失败不应该影响API响应
            error_log("Failed to write API log: " . $e->getMessage());
        }
    }
    
    /**
     * 获取客户端IP地址
     */
    private static function getClientIP() {
        $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
        
        foreach ($ipKeys as $key) {
            if (!empty($_SERVER[$key])) {
                $ip = $_SERVER[$key];
                // 处理多个IP的情况（X-Forwarded-For可能包含多个IP）
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                // 验证IP格式
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    }
    
    /**
     * 处理CORS预检请求
     */
    public static function handleCORS() {
        if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
            header('Access-Control-Allow-Origin: *');
            header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
            header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
            header('Access-Control-Max-Age: 86400'); // 24小时
            http_response_code(200);
            exit;
        }
    }
    
    /**
     * 验证请求方法
     */
    public static function validateMethod($allowedMethods) {
        $currentMethod = $_SERVER['REQUEST_METHOD'];
        
        if (!in_array($currentMethod, $allowedMethods)) {
            self::error("不支持的请求方法: {$currentMethod}", 405);
        }
    }
    
    /**
     * 验证必需参数
     */
    public static function validateRequired($data, $requiredFields) {
        $missing = [];
        
        foreach ($requiredFields as $field) {
            if (!isset($data[$field]) || $data[$field] === '' || $data[$field] === null) {
                $missing[] = $field;
            }
        }
        
        if (!empty($missing)) {
            self::validationError($missing, '缺少必需参数: ' . implode(', ', $missing));
        }
    }
    
    /**
     * 获取请求数据
     */
    public static function getRequestData() {
        $method = $_SERVER['REQUEST_METHOD'];
        
        switch ($method) {
            case 'GET':
                return $_GET;
            case 'POST':
            case 'PUT':
            case 'DELETE':
                $input = file_get_contents('php://input');
                $data = json_decode($input, true);
                
                // 如果JSON解析失败，尝试解析表单数据
                if (json_last_error() !== JSON_ERROR_NONE) {
                    parse_str($input, $data);
                }
                
                return $data ?: [];
            default:
                return [];
        }
    }
    
    /**
     * 格式化数据大小
     */
    public static function formatBytes($bytes, $precision = 2) {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }
    
    /**
     * 安全地输出数据（防止XSS）
     */
    public static function sanitize($data) {
        if (is_array($data)) {
            return array_map([self::class, 'sanitize'], $data);
        }
        
        return htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
    }
}

// 自动处理CORS预检请求
Response::handleCORS();
?>
