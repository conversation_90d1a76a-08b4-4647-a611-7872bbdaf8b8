/**
 * 认证处理器
 * 处理登录状态检查和跳转逻辑
 */

class AuthHandler {
    constructor() {
        this.isLoggedIn = false;
        this.userInfo = null;
        
        this.init();
    }

    /**
     * 初始化
     */
    init() {
        this.checkLoginStatus();
        this.bindEvents();
        this.updateUI();
    }

    /**
     * 检查登录状态
     */
    checkLoginStatus() {
        // 从localStorage检查登录状态
        const token = localStorage.getItem('userToken');
        const userInfo = localStorage.getItem('userInfo');
        
        if (token && userInfo) {
            try {
                this.userInfo = JSON.parse(userInfo);
                this.isLoggedIn = true;
            } catch (error) {
                console.error('Parse user info error:', error);
                this.clearLoginData();
            }
        }
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // 登录/注册按钮
        const loginRegisterBtn = document.getElementById('loginRegisterBtn');
        if (loginRegisterBtn) {
            loginRegisterBtn.addEventListener('click', () => this.handleLoginRegisterClick());
        }

        // 用户图标按钮
        const userIconBtn = document.getElementById('userIconBtn');
        if (userIconBtn) {
            userIconBtn.addEventListener('click', () => this.handleUserIconClick());
        }

        // 我的按钮（底部导航）
        const profileNavBtn = document.getElementById('profileNavBtn');
        if (profileNavBtn) {
            profileNavBtn.addEventListener('click', (e) => this.handleProfileNavClick(e));
        }

        // 监听登录成功事件
        document.addEventListener('loginSuccess', (e) => this.handleLoginSuccess(e.detail));
        
        // 监听登出事件
        document.addEventListener('logout', () => this.handleLogout());
    }

    /**
     * 处理登录/注册按钮点击
     */
    handleLoginRegisterClick() {
        if (this.isLoggedIn) {
            // 已登录，显示用户菜单
            this.showUserMenu();
        } else {
            // 未登录，跳转到登录页面
            this.redirectToLogin();
        }
    }

    /**
     * 处理用户图标点击
     */
    handleUserIconClick() {
        if (this.isLoggedIn) {
            // 已登录，显示用户菜单
            this.showUserMenu();
        } else {
            // 未登录，跳转到登录页面
            this.redirectToLogin();
        }
    }

    /**
     * 处理我的按钮点击
     */
    handleProfileNavClick(e) {
        if (!this.isLoggedIn) {
            // 阻止默认导航行为
            e.preventDefault();
            e.stopPropagation();
            
            // 跳转到登录页面
            this.redirectToLogin();
            return false;
        }
        
        // 已登录，允许正常导航
        return true;
    }

    /**
     * 跳转到登录页面
     */
    redirectToLogin() {
        // 保存当前页面URL，登录后返回
        const currentUrl = window.location.href;
        localStorage.setItem('returnUrl', currentUrl);
        
        // 跳转到登录页面
        window.location.href = '../login.html';
    }

    /**
     * 显示用户菜单
     */
    showUserMenu() {
        // 创建用户菜单
        const menu = this.createUserMenu();
        document.body.appendChild(menu);
        
        // 添加点击外部关闭菜单的事件
        setTimeout(() => {
            document.addEventListener('click', (e) => {
                if (!menu.contains(e.target)) {
                    this.closeUserMenu();
                }
            }, { once: true });
        }, 100);
    }

    /**
     * 创建用户菜单
     */
    createUserMenu() {
        const menu = document.createElement('div');
        menu.className = 'user-menu';
        menu.innerHTML = `
            <div class="user-menu-content">
                <div class="user-info">
                    <div class="user-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <div class="user-details">
                        <div class="user-name">${this.userInfo?.name || '用户'}</div>
                        <div class="user-phone">${this.userInfo?.phone || ''}</div>
                    </div>
                </div>
                <div class="menu-divider"></div>
                <div class="menu-items">
                    <div class="menu-item" onclick="authHandler.goToProfile()">
                        <i class="fas fa-user-cog"></i>
                        <span>个人中心</span>
                    </div>
                    <div class="menu-item" onclick="authHandler.goToSettings()">
                        <i class="fas fa-cog"></i>
                        <span>设置</span>
                    </div>
                    <div class="menu-item logout" onclick="authHandler.logout()">
                        <i class="fas fa-sign-out-alt"></i>
                        <span>退出登录</span>
                    </div>
                </div>
            </div>
        `;
        
        return menu;
    }

    /**
     * 关闭用户菜单
     */
    closeUserMenu() {
        const menu = document.querySelector('.user-menu');
        if (menu) {
            menu.remove();
        }
    }

    /**
     * 前往个人中心
     */
    goToProfile() {
        this.closeUserMenu();
        window.location.href = '../profile/index.html';
    }

    /**
     * 前往设置页面
     */
    goToSettings() {
        this.closeUserMenu();
        // 这里可以跳转到设置页面
        console.log('Go to settings');
    }

    /**
     * 登出
     */
    logout() {
        this.closeUserMenu();
        
        // 显示确认对话框
        if (confirm('确定要退出登录吗？')) {
            this.handleLogout();
        }
    }

    /**
     * 处理登录成功
     */
    handleLoginSuccess(userInfo) {
        this.isLoggedIn = true;
        this.userInfo = userInfo;
        
        // 保存登录信息
        localStorage.setItem('userToken', userInfo.token);
        localStorage.setItem('userInfo', JSON.stringify(userInfo));
        
        // 更新UI
        this.updateUI();
        
        // 显示成功消息
        this.showToast('登录成功', 'success');
    }

    /**
     * 处理登出
     */
    handleLogout() {
        this.isLoggedIn = false;
        this.userInfo = null;
        
        // 清除登录数据
        this.clearLoginData();
        
        // 更新UI
        this.updateUI();
        
        // 显示消息
        this.showToast('已退出登录', 'info');
    }

    /**
     * 清除登录数据
     */
    clearLoginData() {
        localStorage.removeItem('userToken');
        localStorage.removeItem('userInfo');
        localStorage.removeItem('returnUrl');
    }

    /**
     * 更新UI
     */
    updateUI() {
        const loginRegisterBtn = document.getElementById('loginRegisterBtn');
        const userIconBtn = document.getElementById('userIconBtn');
        
        if (this.isLoggedIn && this.userInfo) {
            // 已登录状态
            if (loginRegisterBtn) {
                loginRegisterBtn.textContent = this.userInfo.name || '用户';
                loginRegisterBtn.classList.add('logged-in');
            }
            
            if (userIconBtn) {
                userIconBtn.classList.add('logged-in');
            }
        } else {
            // 未登录状态
            if (loginRegisterBtn) {
                loginRegisterBtn.textContent = '登录/注册';
                loginRegisterBtn.classList.remove('logged-in');
            }
            
            if (userIconBtn) {
                userIconBtn.classList.remove('logged-in');
            }
        }
    }

    /**
     * 显示提示消息
     */
    showToast(message, type = 'info') {
        // 创建toast元素
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        toast.textContent = message;
        toast.style.cssText = `
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            padding: 12px 24px;
            border-radius: 8px;
            color: white;
            font-size: 14px;
            font-weight: 500;
            z-index: 1000;
            box-shadow: 0 4px 16px rgba(0,0,0,0.15);
            backdrop-filter: blur(10px);
            animation: slideDown 0.3s ease;
        `;
        
        // 设置背景色
        const colors = {
            success: 'rgba(76, 175, 80, 0.9)',
            error: 'rgba(244, 67, 54, 0.9)',
            warning: 'rgba(255, 152, 0, 0.9)',
            info: 'rgba(33, 150, 243, 0.9)'
        };
        toast.style.backgroundColor = colors[type] || colors.info;
        
        // 添加到页面
        document.body.appendChild(toast);
        
        // 自动移除
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 3000);
    }

    /**
     * 获取登录状态
     */
    getLoginStatus() {
        return {
            isLoggedIn: this.isLoggedIn,
            userInfo: this.userInfo
        };
    }
}

// 全局实例
window.authHandler = new AuthHandler();
