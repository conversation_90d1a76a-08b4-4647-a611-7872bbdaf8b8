/**
 * 页面路由器 - 专门处理页面间跳转
 * 提供统一的页面跳转接口
 */

class PageRouter {
    /**
     * 页面路径映射表
     */
    static ROUTES = {
        // 主要页面
        'HOME': 'home/index.html',
        'LOGIN': 'login.html', 
        'REGISTER': 'register.html',
        
        // 功能模块页面
        'MARKET': 'market/index.html',
        'SMART_INVEST': 'smart-invest/index.html',
        'PROFILE': 'profile/index.html',
        
        // 功能子页面（预留）
        'SMART_FOLLOW': 'home/smart-follow.html',
        'INDICATOR_STOCKS': 'home/indicator-stocks.html',
        'ACTIVITY_CENTER': 'home/activity-center.html',
        'MY_WATCHLIST': 'home/my-watchlist.html',
        'DEPOSIT_WITHDRAW': 'home/deposit-withdraw.html',
        'REVERSE_REBATE': 'home/reverse-rebate.html',
        'MY_POSITIONS': 'home/my-positions.html',
        'COMPANY_INFO': 'home/company-info.html',
        'ONLINE_SERVICE': 'home/online-service.html',
        'APP_DOWNLOAD': 'home/app-download.html'
    };

    /**
     * 获取当前页面的基础路径
     * @returns {string} 基础路径
     */
    static getBasePath() {
        const currentPath = window.location.pathname;
        
        // 如果在根目录
        if (currentPath === '/' || currentPath.endsWith('/index.html')) {
            return './pages/';
        }
        
        // 如果在pages目录下
        if (currentPath.includes('/pages/')) {
            const pathAfterPages = currentPath.split('/pages/')[1];
            const pathParts = pathAfterPages.split('/');
            
            // 如果在子目录中（如 /pages/home/<USER>
            if (pathParts.length > 1) {
                return '../';
            } else {
                // 在pages根目录中（如 /pages/login.html）
                return './';
            }
        }
        
        // 默认情况
        return './pages/';
    }

    /**
     * 跳转到指定页面
     * @param {string} pageKey - 页面标识符
     * @param {Object} options - 跳转选项
     * @param {boolean} options.saveReturnUrl - 是否保存当前页面作为返回URL
     * @param {boolean} options.replace - 是否替换当前历史记录
     */
    static navigateTo(pageKey, options = {}) {
        const { saveReturnUrl = false, replace = false } = options;
        
        try {
            // 保存返回URL
            if (saveReturnUrl) {
                localStorage.setItem('returnUrl', window.location.href);
            }

            // 获取目标路径
            const targetPath = this.getTargetPath(pageKey);
            
            // 执行跳转
            if (replace) {
                window.location.replace(targetPath);
            } else {
                window.location.href = targetPath;
            }
            
        } catch (error) {
            console.error('Navigation error:', error);
            // 降级处理：跳转到首页
            this.goToHome();
        }
    }

    /**
     * 获取目标页面路径
     * @param {string} pageKey - 页面标识符
     * @returns {string} 完整的目标路径
     */
    static getTargetPath(pageKey) {
        // 检查是否是预定义的页面标识符
        if (this.ROUTES[pageKey]) {
            return this.getBasePath() + this.ROUTES[pageKey];
        }
        
        // 如果是直接的路径
        if (typeof pageKey === 'string' && pageKey.includes('.html')) {
            return this.getBasePath() + pageKey;
        }
        
        throw new Error(`Invalid page key: ${pageKey}`);
    }

    /**
     * 跳转到首页
     */
    static goToHome() {
        this.navigateTo('HOME');
    }

    /**
     * 跳转到登录页面
     */
    static goToLogin() {
        this.navigateTo('LOGIN', { saveReturnUrl: true });
    }

    /**
     * 跳转到注册页面
     */
    static goToRegister() {
        this.navigateTo('REGISTER', { saveReturnUrl: true });
    }

    /**
     * 跳转到行情页面
     */
    static goToMarket() {
        this.navigateTo('MARKET');
    }

    /**
     * 跳转到智投页面
     */
    static goToSmartInvest() {
        this.navigateTo('SMART_INVEST');
    }

    /**
     * 跳转到个人中心
     */
    static goToProfile() {
        this.navigateTo('PROFILE');
    }

    /**
     * 返回上一页
     * @param {string} fallbackPage - 如果没有历史记录时的降级页面
     */
    static goBack(fallbackPage = 'HOME') {
        // 尝试从localStorage获取返回URL
        const returnUrl = localStorage.getItem('returnUrl');
        
        if (returnUrl) {
            localStorage.removeItem('returnUrl');
            window.location.href = returnUrl;
            return;
        }
        
        // 尝试浏览器历史记录
        if (window.history.length > 1) {
            window.history.back();
            return;
        }
        
        // 降级到指定页面
        this.navigateTo(fallbackPage);
    }

    /**
     * 登录成功后的跳转处理
     */
    static handleLoginSuccess() {
        const returnUrl = localStorage.getItem('returnUrl');
        
        if (returnUrl) {
            localStorage.removeItem('returnUrl');
            window.location.href = returnUrl;
        } else {
            this.goToHome();
        }
    }

    /**
     * 检查页面是否存在
     * @param {string} pageKey - 页面标识符
     * @returns {boolean} 页面是否存在
     */
    static pageExists(pageKey) {
        return this.ROUTES.hasOwnProperty(pageKey);
    }

    /**
     * 获取所有可用的页面列表
     * @returns {Array} 页面标识符数组
     */
    static getAvailablePages() {
        return Object.keys(this.ROUTES);
    }

    /**
     * 获取当前页面的标识符
     * @returns {string} 当前页面标识符
     */
    static getCurrentPageKey() {
        const currentPath = window.location.pathname;
        
        // 遍历路由表找到匹配的页面
        for (const [key, path] of Object.entries(this.ROUTES)) {
            if (currentPath.includes(path)) {
                return key;
            }
        }
        
        return 'UNKNOWN';
    }

    /**
     * 调试信息
     */
    static debug() {
        console.log('PageRouter Debug Info:', {
            currentPath: window.location.pathname,
            currentPageKey: this.getCurrentPageKey(),
            basePath: this.getBasePath(),
            availablePages: this.getAvailablePages()
        });
    }
}

// 全局导出
window.PageRouter = PageRouter;

// 提供简化的全局函数
window.goToPage = (pageKey, options) => PageRouter.navigateTo(pageKey, options);
window.goHome = () => PageRouter.goToHome();
window.goLogin = () => PageRouter.goToLogin();
window.goRegister = () => PageRouter.goToRegister();
window.goBack = (fallback) => PageRouter.goBack(fallback);
