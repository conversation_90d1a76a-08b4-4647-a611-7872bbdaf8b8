/**
 * 国信证券移动端应用 - 工具函数
 * 包含通用的工具函数、格式化函数、验证函数等
 */

class Utils {
    /**
     * 格式化数字
     * @param {number} num 数字
     * @param {object} options 格式化选项
     * @returns {string} 格式化后的字符串
     */
    static formatNumber(num, options = {}) {
        const defaultOptions = {
            locale: 'zh-CN',
            minimumFractionDigits: 0,
            maximumFractionDigits: 2
        };
        
        const formatOptions = { ...defaultOptions, ...options };
        
        if (typeof num !== 'number' || isNaN(num)) {
            return '--';
        }
        
        return num.toLocaleString(formatOptions.locale, formatOptions);
    }

    /**
     * 格式化百分比
     * @param {number} num 数字
     * @param {boolean} showSign 是否显示正负号
     * @returns {string} 格式化后的百分比
     */
    static formatPercent(num, showSign = true) {
        if (typeof num !== 'number' || isNaN(num)) {
            return '--';
        }
        
        const sign = showSign && num > 0 ? '+' : '';
        return `${sign}${num.toFixed(2)}%`;
    }

    /**
     * 格式化货币
     * @param {number} num 数字
     * @param {string} currency 货币符号
     * @returns {string} 格式化后的货币
     */
    static formatCurrency(num, currency = '¥') {
        if (typeof num !== 'number' || isNaN(num)) {
            return '--';
        }
        
        return `${currency}${this.formatNumber(num, { minimumFractionDigits: 2 })}`;
    }

    /**
     * 格式化大数字（万、亿）
     * @param {number} num 数字
     * @returns {string} 格式化后的字符串
     */
    static formatLargeNumber(num) {
        if (typeof num !== 'number' || isNaN(num)) {
            return '--';
        }
        
        if (num >= 100000000) {
            return `${(num / 100000000).toFixed(2)}亿`;
        } else if (num >= 10000) {
            return `${(num / 10000).toFixed(2)}万`;
        } else {
            return this.formatNumber(num);
        }
    }

    /**
     * 格式化时间
     * @param {Date|string|number} date 日期
     * @param {string} format 格式
     * @returns {string} 格式化后的时间
     */
    static formatTime(date, format = 'YYYY-MM-DD HH:mm:ss') {
        if (!date) return '--';
        
        const d = new Date(date);
        if (isNaN(d.getTime())) return '--';
        
        const year = d.getFullYear();
        const month = String(d.getMonth() + 1).padStart(2, '0');
        const day = String(d.getDate()).padStart(2, '0');
        const hours = String(d.getHours()).padStart(2, '0');
        const minutes = String(d.getMinutes()).padStart(2, '0');
        const seconds = String(d.getSeconds()).padStart(2, '0');
        
        return format
            .replace('YYYY', year)
            .replace('MM', month)
            .replace('DD', day)
            .replace('HH', hours)
            .replace('mm', minutes)
            .replace('ss', seconds);
    }

    /**
     * 获取相对时间
     * @param {Date|string|number} date 日期
     * @returns {string} 相对时间描述
     */
    static getRelativeTime(date) {
        if (!date) return '--';
        
        const now = new Date();
        const target = new Date(date);
        const diff = now - target;
        
        const minute = 60 * 1000;
        const hour = 60 * minute;
        const day = 24 * hour;
        
        if (diff < minute) {
            return '刚刚';
        } else if (diff < hour) {
            return `${Math.floor(diff / minute)}分钟前`;
        } else if (diff < day) {
            return `${Math.floor(diff / hour)}小时前`;
        } else if (diff < 7 * day) {
            return `${Math.floor(diff / day)}天前`;
        } else {
            return this.formatTime(target, 'MM-DD');
        }
    }

    /**
     * 防抖函数
     * @param {Function} func 要防抖的函数
     * @param {number} wait 等待时间
     * @returns {Function} 防抖后的函数
     */
    static debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    /**
     * 节流函数
     * @param {Function} func 要节流的函数
     * @param {number} limit 时间限制
     * @returns {Function} 节流后的函数
     */
    static throttle(func, limit) {
        let inThrottle;
        return function(...args) {
            if (!inThrottle) {
                func.apply(this, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }

    /**
     * 深拷贝对象
     * @param {any} obj 要拷贝的对象
     * @returns {any} 拷贝后的对象
     */
    static deepClone(obj) {
        if (obj === null || typeof obj !== 'object') {
            return obj;
        }
        
        if (obj instanceof Date) {
            return new Date(obj.getTime());
        }
        
        if (obj instanceof Array) {
            return obj.map(item => this.deepClone(item));
        }
        
        if (typeof obj === 'object') {
            const clonedObj = {};
            for (const key in obj) {
                if (obj.hasOwnProperty(key)) {
                    clonedObj[key] = this.deepClone(obj[key]);
                }
            }
            return clonedObj;
        }
    }

    /**
     * 生成唯一ID
     * @returns {string} 唯一ID
     */
    static generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    /**
     * 验证手机号
     * @param {string} phone 手机号
     * @returns {boolean} 是否有效
     */
    static validatePhone(phone) {
        return Constants.REGEX_PATTERNS.PHONE.test(phone);
    }

    /**
     * 验证邮箱
     * @param {string} email 邮箱
     * @returns {boolean} 是否有效
     */
    static validateEmail(email) {
        return Constants.REGEX_PATTERNS.EMAIL.test(email);
    }

    /**
     * 验证股票代码
     * @param {string} code 股票代码
     * @returns {boolean} 是否有效
     */
    static validateStockCode(code) {
        return Constants.REGEX_PATTERNS.STOCK_CODE.test(code);
    }

    /**
     * 验证密码强度
     * @param {string} password 密码
     * @returns {object} 验证结果
     */
    static validatePassword(password) {
        const result = {
            isValid: false,
            strength: 'weak',
            messages: []
        };
        
        if (!password) {
            result.messages.push('密码不能为空');
            return result;
        }
        
        if (password.length < 6) {
            result.messages.push('密码长度至少6位');
        }
        
        if (!/[a-zA-Z]/.test(password)) {
            result.messages.push('密码必须包含字母');
        }
        
        if (!/\d/.test(password)) {
            result.messages.push('密码必须包含数字');
        }
        
        if (result.messages.length === 0) {
            result.isValid = true;
            
            // 计算密码强度
            let score = 0;
            if (password.length >= 8) score++;
            if (/[a-z]/.test(password)) score++;
            if (/[A-Z]/.test(password)) score++;
            if (/\d/.test(password)) score++;
            if (/[^a-zA-Z\d]/.test(password)) score++;
            
            if (score >= 4) {
                result.strength = 'strong';
            } else if (score >= 2) {
                result.strength = 'medium';
            }
        }
        
        return result;
    }

    /**
     * 本地存储操作
     */
    static storage = {
        set(key, value) {
            try {
                localStorage.setItem(key, JSON.stringify(value));
                return true;
            } catch (e) {
                console.error('Storage set error:', e);
                return false;
            }
        },
        
        get(key, defaultValue = null) {
            try {
                const item = localStorage.getItem(key);
                return item ? JSON.parse(item) : defaultValue;
            } catch (e) {
                console.error('Storage get error:', e);
                return defaultValue;
            }
        },
        
        remove(key) {
            try {
                localStorage.removeItem(key);
                return true;
            } catch (e) {
                console.error('Storage remove error:', e);
                return false;
            }
        },
        
        clear() {
            try {
                localStorage.clear();
                return true;
            } catch (e) {
                console.error('Storage clear error:', e);
                return false;
            }
        }
    };

    /**
     * URL 操作
     */
    static url = {
        getParams() {
            const params = {};
            const urlParams = new URLSearchParams(window.location.search);
            for (const [key, value] of urlParams) {
                params[key] = value;
            }
            return params;
        },
        
        setParam(key, value) {
            const url = new URL(window.location);
            url.searchParams.set(key, value);
            window.history.replaceState({}, '', url);
        },
        
        removeParam(key) {
            const url = new URL(window.location);
            url.searchParams.delete(key);
            window.history.replaceState({}, '', url);
        }
    };

    /**
     * 设备检测
     */
    static device = {
        isMobile() {
            return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
        },
        
        isIOS() {
            return /iPad|iPhone|iPod/.test(navigator.userAgent);
        },
        
        isAndroid() {
            return /Android/.test(navigator.userAgent);
        },
        
        getScreenSize() {
            return {
                width: window.innerWidth,
                height: window.innerHeight
            };
        }
    };
}

// 导出工具类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = Utils;
} else {
    window.Utils = Utils;
}
