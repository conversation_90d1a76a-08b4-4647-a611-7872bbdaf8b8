/* 
 * 国信证券移动端应用 - 组件样式文件
 * 包含股票指数、功能菜单、新闻、导航等组件样式
 */

/* ===== 股票指数组件 ===== */
.stock-indices {
    display: flex;
    background-color: var(--background-primary);
    margin: var(--spacing-md);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-md) 0;
    box-shadow: var(--shadow-md);
}

.index-item {
    flex: 1;
    text-align: center;
    padding: 0 var(--spacing-sm);
    position: relative;
}

.index-item:not(:last-child)::after {
    content: '';
    position: absolute;
    right: 0;
    top: 10%;
    height: 80%;
    width: 1px;
    background-color: var(--border-color);
}

.index-name {
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-sm);
    font-weight: 500;
}

.index-value {
    font-size: var(--font-size-lg);
    font-weight: bold;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
    font-family: 'Courier New', monospace;
}

.index-change {
    font-size: var(--font-size-xs);
    display: flex;
    flex-direction: column;
    gap: 2px;
    font-weight: 500;
}

.index-change.negative {
    color: var(--success-color);
}

.index-change.positive {
    color: var(--error-color);
}

.index-change span {
    line-height: 1.2;
}

/* ===== 功能菜单组件 ===== */
.function-menu {
    background-color: var(--background-primary);
    margin: var(--spacing-md);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-md);
}

.menu-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: var(--spacing-lg);
}

.menu-row:last-child {
    margin-bottom: 0;
}

.menu-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-sm);
    flex: 1;
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--border-radius-md);
    transition: all 0.3s ease;
    position: relative;
}

.menu-item:hover {
    background-color: rgba(0,0,0,0.02);
    transform: translateY(-2px);
}

.menu-item:active {
    transform: translateY(0);
}

.menu-icon {
    width: 48px;
    height: 48px;
    border-radius: var(--border-radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-xl);
    color: white;
    position: relative;
    overflow: hidden;
    transition: transform 0.3s ease;
}

.menu-item:hover .menu-icon {
    transform: scale(1.1);
}

/* 菜单图标渐变色 */
.menu-icon.yellow { 
    background: linear-gradient(135deg, #ffc107, #ff9800);
    box-shadow: 0 4px 12px rgba(255, 193, 7, 0.3);
}

.menu-icon.red { 
    background: linear-gradient(135deg, #f44336, #e91e63);
    box-shadow: 0 4px 12px rgba(244, 67, 54, 0.3);
}

.menu-icon.gray { 
    background: linear-gradient(135deg, #9e9e9e, #757575);
    box-shadow: 0 4px 12px rgba(158, 158, 158, 0.3);
}

.menu-icon.pink { 
    background: linear-gradient(135deg, #e91e63, #f06292);
    box-shadow: 0 4px 12px rgba(233, 30, 99, 0.3);
}

.menu-icon.blue { 
    background: linear-gradient(135deg, #2196f3, #1976d2);
    box-shadow: 0 4px 12px rgba(33, 150, 243, 0.3);
}

.menu-icon.red-chart { 
    background: linear-gradient(135deg, #f44336, #d32f2f);
    box-shadow: 0 4px 12px rgba(244, 67, 54, 0.3);
}

.menu-icon.green { 
    background: linear-gradient(135deg, #4caf50, #388e3c);
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
}

.menu-icon.pink-circle { 
    background: linear-gradient(135deg, #ff4081, #e91e63);
    box-shadow: 0 4px 12px rgba(255, 64, 129, 0.3);
}

.menu-icon.red-diamond { 
    background: linear-gradient(135deg, #f44336, #c62828);
    box-shadow: 0 4px 12px rgba(244, 67, 54, 0.3);
}

.menu-icon.green-download { 
    background: linear-gradient(135deg, #4caf50, #2e7d32);
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
}

.menu-item span {
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
    text-align: center;
    font-weight: 500;
    transition: color 0.3s ease;
}

.menu-item:hover span {
    color: var(--text-primary);
}

/* ===== 新闻组件 ===== */
.news-section {
    background-color: var(--background-primary);
    margin: var(--spacing-md);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-md);
    box-shadow: var(--shadow-md);
}

.news-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-sm);
    border-bottom: 1px solid var(--border-color);
}

.news-header h3 {
    font-size: var(--font-size-md);
    font-weight: bold;
    color: var(--text-primary);
}

.more-link {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    transition: color 0.3s ease;
}

.more-link:hover {
    color: var(--primary-color);
}

.news-item {
    display: flex;
    gap: var(--spacing-lg);
    align-items: center;
    padding: var(--spacing-sm);
    border-radius: var(--border-radius-md);
    transition: background-color 0.3s ease;
    cursor: pointer;
}

.news-item:hover {
    background-color: rgba(0,0,0,0.02);
}

.news-content {
    flex: 1;
}

.news-content h4 {
    font-size: var(--font-size-sm);
    color: var(--text-primary);
    line-height: 1.4;
    font-weight: 500;
    margin-bottom: var(--spacing-xs);
}

.news-meta {
    font-size: var(--font-size-xs);
    color: var(--text-disabled);
    display: flex;
    gap: var(--spacing-md);
}

.news-image {
    width: 80px;
    height: 60px;
    border-radius: var(--border-radius-md);
    overflow: hidden;
    background: linear-gradient(45deg, var(--error-color), #e53935);
    flex-shrink: 0;
}

.news-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.news-item:hover .news-image img {
    transform: scale(1.05);
}

/* ===== 底部导航组件 ===== */
.bottom-nav {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: var(--background-primary);
    display: flex;
    border-top: 1px solid var(--border-color);
    z-index: 100;
    backdrop-filter: blur(10px);
    background-color: rgba(255, 255, 255, 0.95);
}

.nav-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: var(--spacing-sm);
    cursor: pointer;
    color: var(--text-secondary);
    transition: all 0.3s ease;
    position: relative;
}

.nav-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 2px;
    background-color: var(--primary-color);
    transition: width 0.3s ease;
}

.nav-item.active {
    color: var(--primary-color);
}

.nav-item.active::before {
    width: 24px;
}

.nav-item:hover {
    color: var(--primary-color);
    background-color: rgba(25, 118, 210, 0.05);
}

.nav-item i {
    font-size: var(--font-size-xl);
    margin-bottom: var(--spacing-xs);
    transition: transform 0.3s ease;
}

.nav-item:active i {
    transform: scale(0.9);
}

.nav-item span {
    font-size: var(--font-size-xs);
    font-weight: 500;
}

/* ===== Toast 组件 ===== */
.toast {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    padding: var(--spacing-lg) var(--spacing-xl);
    border-radius: var(--border-radius-md);
    z-index: 1000;
    font-size: var(--font-size-sm);
    font-weight: 500;
    box-shadow: var(--shadow-lg);
    backdrop-filter: blur(10px);
}

.toast.success {
    background-color: rgba(76, 175, 80, 0.9);
}

.toast.error {
    background-color: rgba(244, 67, 54, 0.9);
}

.toast.warning {
    background-color: rgba(255, 152, 0, 0.9);
}

.toast.info {
    background-color: rgba(33, 150, 243, 0.9);
}

/* ===== 响应式设计 ===== */
@media (max-width: 480px) {
    .menu-icon {
        width: 40px;
        height: 40px;
        font-size: var(--font-size-lg);
    }
    
    .menu-item span {
        font-size: 11px;
    }
    
    .news-image {
        width: 60px;
        height: 45px;
    }
    
    .function-menu {
        padding: var(--spacing-md);
    }
}

@media (max-width: 360px) {
    .menu-row {
        flex-wrap: wrap;
        gap: var(--spacing-sm);
    }
    
    .menu-item {
        flex: 0 0 calc(20% - var(--spacing-sm));
        min-width: 60px;
    }
    
    .stock-indices {
        flex-direction: column;
        gap: var(--spacing-sm);
    }
    
    .index-item:not(:last-child)::after {
        display: none;
    }
}
