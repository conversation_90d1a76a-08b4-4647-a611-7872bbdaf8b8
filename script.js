// 国信证券移动端应用 JavaScript

class StockApp {
    constructor() {
        this.init();
        this.bindEvents();
        this.startStockDataUpdate();
    }

    init() {
        // 初始化应用
        this.updateTime();
        this.loadStockData();
        this.loadNews();
    }

    bindEvents() {
        // 绑定事件监听器
        this.bindNavigationEvents();
        this.bindMenuEvents();
        this.bindButtonEvents();
    }

    bindNavigationEvents() {
        // 底部导航点击事件
        const navItems = document.querySelectorAll('.nav-item');
        navItems.forEach(item => {
            item.addEventListener('click', (e) => {
                // 移除所有active类
                navItems.forEach(nav => nav.classList.remove('active'));
                // 添加active类到当前点击的项
                e.currentTarget.classList.add('active');
                
                const navText = e.currentTarget.querySelector('span').textContent;
                this.handleNavigation(navText);
            });
        });

        // 系统导航事件
        document.querySelector('.nav-button.back').addEventListener('click', () => {
            window.history.back();
        });

        document.querySelector('.nav-button.home').addEventListener('click', () => {
            window.location.href = '/';
        });
    }

    bindMenuEvents() {
        // 功能菜单点击事件
        const menuItems = document.querySelectorAll('.menu-item');
        menuItems.forEach(item => {
            item.addEventListener('click', (e) => {
                const menuText = e.currentTarget.querySelector('span').textContent;
                this.handleMenuClick(menuText);
            });
        });
    }

    bindButtonEvents() {
        // 开户按钮点击事件
        document.querySelector('.open-account-btn').addEventListener('click', () => {
            this.handleOpenAccount();
        });

        // 登录注册点击事件
        document.querySelector('.login-register').addEventListener('click', () => {
            this.handleLoginRegister();
        });

        // 搜索按钮点击事件
        document.querySelector('.header-right .fa-search').addEventListener('click', () => {
            this.handleSearch();
        });

        // 更多新闻点击事件
        document.querySelector('.more-link').addEventListener('click', () => {
            this.handleMoreNews();
        });
    }

    handleNavigation(navText) {
        console.log(`导航到: ${navText}`);
        // 这里可以添加路由逻辑
        switch(navText) {
            case '首页':
                this.showHomePage();
                break;
            case '行情':
                this.showMarketPage();
                break;
            case '智投':
                this.showInvestmentPage();
                break;
            case '我的':
                this.showProfilePage();
                break;
        }
    }

    handleMenuClick(menuText) {
        console.log(`点击菜单: ${menuText}`);
        // 添加点击反馈
        this.showToast(`正在打开${menuText}...`);
        
        // 这里可以添加具体的功能逻辑
        switch(menuText) {
            case '智能跟投':
                this.openSmartInvestment();
                break;
            case '指标金股':
                this.openGoldenStocks();
                break;
            case '立即开户':
            case '开户':
                this.handleOpenAccount();
                break;
            // 添加其他菜单项的处理
        }
    }

    handleOpenAccount() {
        this.showToast('正在跳转到开户页面...');
        // 这里可以跳转到开户页面或调用开户API
        setTimeout(() => {
            window.open('account-opening.html', '_blank');
        }, 1000);
    }

    handleLoginRegister() {
        this.showToast('正在跳转到登录页面...');
        // 跳转到登录页面
        setTimeout(() => {
            window.location.href = 'login.html';
        }, 1000);
    }

    handleSearch() {
        // 显示搜索框或跳转到搜索页面
        const searchTerm = prompt('请输入股票代码或名称:');
        if (searchTerm) {
            this.searchStock(searchTerm);
        }
    }

    handleMoreNews() {
        this.showToast('正在加载更多新闻...');
        // 跳转到新闻列表页面
        setTimeout(() => {
            window.location.href = 'news.html';
        }, 1000);
    }

    updateTime() {
        const now = new Date();
        const timeString = now.toLocaleTimeString('zh-CN', { 
            hour12: false, 
            hour: '2-digit', 
            minute: '2-digit' 
        });
        document.querySelector('.time').textContent = timeString;
    }

    loadStockData() {
        // 模拟加载股票数据
        const stockData = {
            '上证指数': { value: 3348.37, change: -31.82, percent: -0.94 },
            '深证成指': { value: 10132.41, change: -87.21, percent: -0.85 },
            '创业板指': { value: 2021.5, change: -24.07, percent: -1.18 }
        };

        // 更新股票数据显示
        const indexItems = document.querySelectorAll('.index-item');
        indexItems.forEach((item, index) => {
            const indexName = item.querySelector('.index-name').textContent;
            const data = stockData[indexName];
            if (data) {
                this.updateStockDisplay(item, data);
            }
        });
    }

    updateStockDisplay(element, data) {
        const valueElement = element.querySelector('.index-value');
        const changeElement = element.querySelector('.index-change');
        
        // 添加数字动画效果
        this.animateNumber(valueElement, data.value);
        
        // 更新涨跌信息
        const changeSpans = changeElement.querySelectorAll('span');
        changeSpans[0].textContent = data.change > 0 ? `+${data.change}` : data.change;
        changeSpans[1].textContent = data.percent > 0 ? `+${data.percent}%` : `${data.percent}%`;
        
        // 设置颜色
        changeElement.className = data.change >= 0 ? 'index-change positive' : 'index-change negative';
    }

    animateNumber(element, targetValue) {
        const currentValue = parseFloat(element.textContent) || 0;
        const increment = (targetValue - currentValue) / 20;
        let current = currentValue;
        
        const animation = setInterval(() => {
            current += increment;
            if ((increment > 0 && current >= targetValue) || (increment < 0 && current <= targetValue)) {
                current = targetValue;
                clearInterval(animation);
            }
            element.textContent = current.toFixed(2);
        }, 50);
    }

    startStockDataUpdate() {
        // 每30秒更新一次股票数据
        setInterval(() => {
            this.loadStockData();
        }, 30000);

        // 每分钟更新一次时间
        setInterval(() => {
            this.updateTime();
        }, 60000);
    }

    loadNews() {
        // 模拟加载新闻数据
        const newsData = [
            {
                title: '下周解禁股名单来了 10股解禁市值超6亿元',
                image: 'news-image.jpg',
                time: '2024-01-15 14:30'
            }
        ];

        // 这里可以添加更多新闻加载逻辑
    }

    searchStock(searchTerm) {
        this.showToast(`正在搜索: ${searchTerm}`);
        // 这里可以调用股票搜索API
        console.log(`搜索股票: ${searchTerm}`);
    }

    showToast(message) {
        // 创建toast提示
        const toast = document.createElement('div');
        toast.className = 'toast';
        toast.textContent = message;
        toast.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background-color: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 12px 24px;
            border-radius: 8px;
            z-index: 1000;
            font-size: 14px;
        `;
        
        document.body.appendChild(toast);
        
        setTimeout(() => {
            document.body.removeChild(toast);
        }, 2000);
    }

    // 页面显示方法
    showHomePage() {
        console.log('显示首页');
    }

    showMarketPage() {
        console.log('显示行情页面');
    }

    showInvestmentPage() {
        console.log('显示智投页面');
    }

    showProfilePage() {
        console.log('显示个人页面');
    }

    openSmartInvestment() {
        console.log('打开智能跟投');
    }

    openGoldenStocks() {
        console.log('打开指标金股');
    }
}

// 页面加载完成后初始化应用
document.addEventListener('DOMContentLoaded', () => {
    new StockApp();
});

// 添加一些实用工具函数
const Utils = {
    formatNumber(num) {
        return num.toLocaleString('zh-CN');
    },

    formatPercent(num) {
        return `${num > 0 ? '+' : ''}${num.toFixed(2)}%`;
    },

    formatCurrency(num) {
        return `¥${num.toLocaleString('zh-CN', { minimumFractionDigits: 2 })}`;
    },

    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
};
