<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>国信证券</title>
    <style>
        /* 基础样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --secondary-color: #ff6b35;
            --accent-color: #ff8f00;
            --spacing-xs: 4px;
            --spacing-sm: 8px;
            --spacing-md: 16px;
            --spacing-lg: 24px;
            --border-radius-lg: 12px;
            --font-size-xs: 12px;
            --font-size-sm: 14px;
            --font-size-md: 16px;
            --font-size-lg: 18px;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            color: #333;
            line-height: 1.4;
            font-size: var(--font-size-md);
        }

        /* 营销横幅 */
        .banner {
            background: linear-gradient(135deg, var(--secondary-color), var(--accent-color));
            margin: var(--spacing-md);
            border-radius: var(--border-radius-lg);
            overflow: hidden;
            position: relative;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .banner-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--spacing-lg);
            color: white;
            gap: 0;
        }

        .banner-text {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-xs);
            flex: 1;
            margin-left: 40px;
        }

        .banner-text h2 {
            font-size: 3rem;
            font-weight: bold;
            margin-bottom: var(--spacing-xs);
            text-shadow: 0 1px 2px rgba(0,0,0,0.1);
        }

        .banner-text p {
            font-size: var(--font-size-lg);
            margin-bottom: var(--spacing-xs);
            opacity: 0.9;
        }

        .open-account-btn {
            background-color: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: var(--spacing-sm) var(--spacing-md);
            border-radius: 20px;
            font-size: var(--font-size-sm);
            margin-top: var(--spacing-sm);
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
            width: fit-content;
        }

        .open-account-btn:hover {
            background-color: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .banner-image {
            position: relative;
            width: 180px;
            height: 160px;
            flex-shrink: 0;
            margin-right: 40px;
        }

        .phone-mockup {
            width: 130px;
            height: 160px;
            background: linear-gradient(45deg, rgba(255,255,255,0.3), rgba(255,255,255,0.1));
            border-radius: var(--border-radius-lg);
            position: absolute;
            right: 0;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }

        .coins {
            position: absolute;
            top: 10px;
            right: 35px;
            width: 60px;
            height: 30px;
            background: radial-gradient(circle, #ffd700, #ffb300);
            border-radius: 50%;
            opacity: 0.9;
            animation: bounce 4s linear infinite;
            z-index: 10;
        }

        @keyframes bounce {
            0% { transform: translateY(0px); }
            50% { transform: translateY(115px); }
            100% { transform: translateY(0px); }
        }

        /* 响应式设计 */
        @media (max-width: 480px) {
            .banner {
                margin: var(--spacing-sm);
            }

            .banner-content {
                padding: var(--spacing-md);
                gap: 0;
            }

            .banner-text h2 {
                font-size: var(--font-size-lg);
            }

            .banner-text p {
                font-size: var(--font-size-xs);
            }

            .banner-image {
                width: 140px;
                height: 130px;
                margin-right: 25px;
            }

            .phone-mockup {
                width: 100px;
                height: 130px;
            }

            .coins {
                width: 40px;
                height: 20px;
                right: 20px;
                top: 8px;
            }

            @keyframes bounce {
                0% { transform: translateY(0px); }
                50% { transform: translateY(60px); }
                100% { transform: translateY(0px); }
            }
        }

        @media (max-width: 360px) {
            .banner {
                margin: var(--spacing-xs);
            }

            .banner-content {
                padding: var(--spacing-sm);
                gap: 0;
            }

            .banner-text h2 {
                font-size: var(--font-size-md);
            }

            .banner-image {
                width: 120px;
                height: 110px;
                margin-right: 20px;
            }

            .phone-mockup {
                width: 85px;
                height: 110px;
            }

            .coins {
                width: 35px;
                height: 18px;
                right: 15px;
                top: 6px;
            }

            @keyframes bounce {
                0% { transform: translateY(0px); }
                50% { transform: translateY(45px); }
                100% { transform: translateY(0px); }
            }
        }
    </style>
</head>
<body>
    <!-- 营销横幅 -->
    <section class="banner">
        <div class="banner-content">
            <div class="banner-text">
                <h2>投资正当时</h2>
                <h2>炒股选国信</h2>
                <p>交易安全可靠</p>
                <p>国企上市大券商</p>
                <button class="open-account-btn">
                    <span>立即开户</span>
                    <span>→</span>
                </button>
            </div>
            <div class="banner-image">
                <div class="phone-mockup"></div>
                <div class="coins"></div>
            </div>
        </div>
    </section>
</body>
</html>
