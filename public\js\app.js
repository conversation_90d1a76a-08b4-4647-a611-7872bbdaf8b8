/**
 * 国信证券移动端应用 - 主应用文件
 * 负责应用的初始化和组件协调
 */

class GuosenApp {
    constructor() {
        this.components = {};
        this.isInitialized = false;
        this.startTime = Date.now();

        this.init();
    }

    /**
     * 初始化应用
     */
    async init() {
        try {
            // 显示加载状态
            this.showLoadingState();

            // 初始化基础功能
            await this.initializeCore();

            // 初始化组件
            await this.initializeComponents();

            // 初始化全局事件
            this.initializeGlobalEvents();

            // 初始化完成
            this.onInitialized();

        } catch (error) {
            console.error('App initialization failed:', error);
            this.showError('应用初始化失败，请刷新页面重试');
        }
    }

    /**
     * 显示加载状态
     */
    showLoadingState() {
        // 可以在这里添加全局加载动画
        document.body.classList.add('app-loading');
    }

    /**
     * 隐藏加载状态
     */
    hideLoadingState() {
        document.body.classList.remove('app-loading');
    }

    /**
     * 初始化核心功能
     */
    async initializeCore() {
        // 初始化Toast组件
        this.initializeToast();

        // 检查用户登录状态
        this.checkUserLoginStatus();

        // 初始化页面统计
        this.initializeAnalytics();
    }

    /**
     * 初始化组件
     */
    async initializeComponents() {
        try {
            // 初始化股票数据组件
            this.components.stockData = new StockDataComponent();

            // 初始化导航组件
            this.components.navigation = new NavigationComponent();

            // 初始化菜单组件
            this.components.menu = new MenuComponent();

            // 将组件暴露到全局，方便其他模块使用
            window.stockDataComponent = this.components.stockData;
            window.navigationComponent = this.components.navigation;
            window.menuComponent = this.components.menu;

        } catch (error) {
            console.error('Component initialization failed:', error);
            throw error;
        }
    }

    /**
     * 初始化Toast组件
     */
    initializeToast() {
        window.Toast = {
            show: (message, type = 'info', duration = 3000) => {
                this.showToast(message, type, duration);
            }
        };
    }

    /**
     * 显示Toast消息
     */
    showToast(message, type = 'info', duration = 3000) {
        // 移除现有的toast
        const existingToast = document.querySelector('.toast');
        if (existingToast) {
            existingToast.remove();
        }

        // 创建新的toast
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.textContent = message;

        // 添加样式
        Object.assign(toast.style, {
            position: 'fixed',
            top: '20px',
            left: '50%',
            transform: 'translateX(-50%)',
            padding: '12px 24px',
            borderRadius: '8px',
            zIndex: '1000',
            fontSize: '14px',
            fontWeight: '500',
            boxShadow: '0 4px 16px rgba(0,0,0,0.15)',
            backdropFilter: 'blur(10px)',
            animation: 'slideDown 0.3s ease'
        });

        // 设置背景色
        const colors = {
            success: 'rgba(76, 175, 80, 0.9)',
            error: 'rgba(244, 67, 54, 0.9)',
            warning: 'rgba(255, 152, 0, 0.9)',
            info: 'rgba(33, 150, 243, 0.9)'
        };

        toast.style.backgroundColor = colors[type] || colors.info;
        toast.style.color = 'white';

        document.body.appendChild(toast);

        // 自动移除
        setTimeout(() => {
            if (toast.parentNode) {
                toast.style.animation = 'slideUp 0.3s ease';
                setTimeout(() => {
                    if (toast.parentNode) {
                        toast.remove();
                    }
                }, 300);
            }
        }, duration);
    }

    /**
     * 检查用户登录状态
     */
    checkUserLoginStatus() {
        const token = Utils.storage.get(Constants.STORAGE_KEYS.USER_TOKEN);
        const userInfo = Utils.storage.get(Constants.STORAGE_KEYS.USER_INFO);

        if (token && userInfo) {
            this.setUserLoggedIn(userInfo);
        } else {
            this.setUserLoggedOut();
        }
    }

    /**
     * 设置用户已登录状态
     */
    setUserLoggedIn(userInfo) {
        document.body.classList.add('user-logged-in');
        document.body.classList.remove('user-logged-out');

        // 更新登录按钮文本
        const loginBtn = document.querySelector('.login-register');
        if (loginBtn && userInfo.nickname) {
            loginBtn.textContent = userInfo.nickname;
        }

        // 触发登录状态变化事件
        document.dispatchEvent(new CustomEvent('userLoginStatusChanged', {
            detail: { isLoggedIn: true, userInfo }
        }));
    }

    /**
     * 设置用户已登出状态
     */
    setUserLoggedOut() {
        document.body.classList.add('user-logged-out');
        document.body.classList.remove('user-logged-in');

        // 恢复登录按钮文本
        const loginBtn = document.querySelector('.login-register');
        if (loginBtn) {
            loginBtn.textContent = '登录/注册';
        }

        // 触发登录状态变化事件
        document.dispatchEvent(new CustomEvent('userLoginStatusChanged', {
            detail: { isLoggedIn: false, userInfo: null }
        }));
    }

    /**
     * 初始化页面统计
     */
    initializeAnalytics() {
        // 记录页面访问
        this.trackPageView();

        // 记录应用启动
        this.trackAppStart();
    }

    /**
     * 初始化全局事件
     */
    initializeGlobalEvents() {
        // 页面可见性变化
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.onPageHidden();
            } else {
                this.onPageVisible();
            }
        });

        // 网络状态变化
        window.addEventListener('online', () => {
            this.onNetworkOnline();
        });

        window.addEventListener('offline', () => {
            this.onNetworkOffline();
        });

        // 页面卸载
        window.addEventListener('beforeunload', () => {
            this.onBeforeUnload();
        });

        // 错误处理
        window.addEventListener('error', (event) => {
            this.onGlobalError(event);
        });

        // 未处理的Promise拒绝
        window.addEventListener('unhandledrejection', (event) => {
            this.onUnhandledRejection(event);
        });
    }

    /**
     * 应用初始化完成
     */
    onInitialized() {
        this.isInitialized = true;
        this.hideLoadingState();

        const loadTime = Date.now() - this.startTime;
        console.log(`App initialized in ${loadTime}ms`);

        // 触发初始化完成事件
        document.dispatchEvent(new CustomEvent('appInitialized', {
            detail: { loadTime }
        }));

        // 显示欢迎消息
        if (Constants.APP_CONFIG.DEBUG) {
            this.showToast('应用加载完成', 'success', 2000);
        }
    }

    /**
     * 页面隐藏时的处理
     */
    onPageHidden() {
        // 暂停不必要的更新
        if (this.components.stockData) {
            this.components.stockData.stopAutoUpdate();
        }
    }

    /**
     * 页面可见时的处理
     */
    onPageVisible() {
        // 恢复更新
        if (this.components.stockData) {
            this.components.stockData.startAutoUpdate();
        }

        // 刷新数据
        this.refreshData();
    }

    /**
     * 网络连接恢复
     */
    onNetworkOnline() {
        this.showToast('网络连接已恢复', 'success');
        this.refreshData();
    }

    /**
     * 网络连接断开
     */
    onNetworkOffline() {
        this.showToast('网络连接已断开', 'warning');
    }

    /**
     * 页面卸载前的处理
     */
    onBeforeUnload() {
        // 保存用户状态
        Utils.storage.set(Constants.STORAGE_KEYS.LAST_VISIT, Date.now());

        // 清理资源
        this.cleanup();
    }

    /**
     * 全局错误处理
     */
    onGlobalError(event) {
        console.error('Global error:', event.error);

        if (Constants.APP_CONFIG.DEBUG) {
            this.showToast('发生错误，请查看控制台', 'error');
        }
    }

    /**
     * 未处理的Promise拒绝
     */
    onUnhandledRejection(event) {
        console.error('Unhandled promise rejection:', event.reason);

        if (Constants.APP_CONFIG.DEBUG) {
            this.showToast('Promise错误，请查看控制台', 'error');
        }
    }

    /**
     * 刷新数据
     */
    async refreshData() {
        try {
            if (this.components.stockData) {
                await this.components.stockData.refresh();
            }
        } catch (error) {
            console.error('Data refresh failed:', error);
        }
    }

    /**
     * 页面访问统计
     */
    trackPageView() {
        if (Constants.APP_CONFIG.DEBUG) {
            console.log('Page view tracked');
        }
    }

    /**
     * 应用启动统计
     */
    trackAppStart() {
        if (Constants.APP_CONFIG.DEBUG) {
            console.log('App start tracked');
        }
    }

    /**
     * 显示错误信息
     */
    showError(message) {
        this.showToast(message, 'error', 5000);
    }

    /**
     * 清理资源
     */
    cleanup() {
        // 清理组件
        Object.values(this.components).forEach(component => {
            if (component && typeof component.destroy === 'function') {
                component.destroy();
            }
        });
    }

    /**
     * 获取应用信息
     */
    getAppInfo() {
        return {
            name: Constants.APP_CONFIG.NAME,
            version: Constants.APP_CONFIG.VERSION,
            isInitialized: this.isInitialized,
            components: Object.keys(this.components)
        };
    }
}

// 页面加载完成后初始化应用
document.addEventListener('DOMContentLoaded', () => {
    window.guosenApp = new GuosenApp();
});

// 导出应用类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = GuosenApp;
} else {
    window.GuosenApp = GuosenApp;
}
