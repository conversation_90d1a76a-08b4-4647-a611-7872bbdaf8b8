<?php
/**
 * 国信证券移动端API接口
 * 提供股票数据、用户认证、新闻等功能的后端支持
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

class StockAPI {
    private $db;
    
    public function __construct() {
        $this->initDatabase();
    }
    
    private function initDatabase() {
        // 数据库连接配置
        $host = 'localhost';
        $dbname = 'guosen_securities';
        $username = 'root';
        $password = '';
        
        try {
            $this->db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
            $this->db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        } catch(PDOException $e) {
            $this->sendError('数据库连接失败: ' . $e->getMessage());
        }
    }
    
    public function handleRequest() {
        $method = $_SERVER['REQUEST_METHOD'];
        $path = $_GET['path'] ?? '';
        
        switch($path) {
            case 'stock-data':
                $this->getStockData();
                break;
            case 'news':
                $this->getNews();
                break;
            case 'login':
                $this->handleLogin();
                break;
            case 'register':
                $this->handleRegister();
                break;
            case 'user-profile':
                $this->getUserProfile();
                break;
            case 'search':
                $this->searchStocks();
                break;
            default:
                $this->sendError('无效的API路径');
        }
    }
    
    /**
     * 获取股票数据
     */
    public function getStockData() {
        try {
            // 模拟实时股票数据
            $stockData = [
                [
                    'name' => '上证指数',
                    'code' => '000001',
                    'value' => 3348.37 + (rand(-100, 100) / 100),
                    'change' => -31.82 + (rand(-50, 50) / 100),
                    'percent' => -0.94 + (rand(-100, 100) / 10000),
                    'volume' => rand(1000000, 9999999),
                    'timestamp' => time()
                ],
                [
                    'name' => '深证成指',
                    'code' => '399001',
                    'value' => 10132.41 + (rand(-200, 200) / 100),
                    'change' => -87.21 + (rand(-100, 100) / 100),
                    'percent' => -0.85 + (rand(-100, 100) / 10000),
                    'volume' => rand(1000000, 9999999),
                    'timestamp' => time()
                ],
                [
                    'name' => '创业板指',
                    'code' => '399006',
                    'value' => 2021.5 + (rand(-50, 50) / 100),
                    'change' => -24.07 + (rand(-30, 30) / 100),
                    'percent' => -1.18 + (rand(-100, 100) / 10000),
                    'volume' => rand(500000, 5999999),
                    'timestamp' => time()
                ]
            ];
            
            $this->sendSuccess($stockData);
        } catch(Exception $e) {
            $this->sendError('获取股票数据失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 获取新闻列表
     */
    public function getNews() {
        try {
            $page = $_GET['page'] ?? 1;
            $limit = $_GET['limit'] ?? 10;
            
            // 模拟新闻数据
            $newsData = [
                [
                    'id' => 1,
                    'title' => '下周解禁股名单来了 10股解禁市值超6亿元',
                    'summary' => '据统计，下周（1月15日-1月19日）共有10只股票解禁，解禁市值合计超过6亿元...',
                    'image' => 'https://example.com/news1.jpg',
                    'publish_time' => '2024-01-15 14:30:00',
                    'source' => '财经新闻',
                    'views' => 1250
                ],
                [
                    'id' => 2,
                    'title' => 'A股三大指数集体收跌 创业板指跌超1%',
                    'summary' => '今日A股三大指数集体收跌，上证指数跌0.94%，深证成指跌0.85%，创业板指跌1.18%...',
                    'image' => 'https://example.com/news2.jpg',
                    'publish_time' => '2024-01-15 15:00:00',
                    'source' => '市场快讯',
                    'views' => 980
                ],
                [
                    'id' => 3,
                    'title' => '机构调研热度不减 科技股受关注',
                    'summary' => '本周机构调研活动依然活跃，科技类股票成为调研重点，多家公司接待机构调研...',
                    'image' => 'https://example.com/news3.jpg',
                    'publish_time' => '2024-01-15 13:45:00',
                    'source' => '投资研究',
                    'views' => 756
                ]
            ];
            
            $this->sendSuccess([
                'news' => $newsData,
                'total' => count($newsData),
                'page' => $page,
                'limit' => $limit
            ]);
        } catch(Exception $e) {
            $this->sendError('获取新闻失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 用户登录
     */
    public function handleLogin() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->sendError('请使用POST方法');
            return;
        }
        
        $input = json_decode(file_get_contents('php://input'), true);
        $username = $input['username'] ?? '';
        $password = $input['password'] ?? '';
        
        if (empty($username) || empty($password)) {
            $this->sendError('用户名和密码不能为空');
            return;
        }
        
        try {
            // 这里应该查询数据库验证用户
            // 为演示目的，使用简单的验证逻辑
            if ($username === 'demo' && $password === '123456') {
                $token = $this->generateToken($username);
                $this->sendSuccess([
                    'token' => $token,
                    'user' => [
                        'id' => 1,
                        'username' => $username,
                        'nickname' => '演示用户',
                        'avatar' => 'https://example.com/avatar.jpg',
                        'phone' => '138****8888',
                        'email' => '<EMAIL>'
                    ]
                ]);
            } else {
                $this->sendError('用户名或密码错误');
            }
        } catch(Exception $e) {
            $this->sendError('登录失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 用户注册
     */
    public function handleRegister() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->sendError('请使用POST方法');
            return;
        }
        
        $input = json_decode(file_get_contents('php://input'), true);
        $username = $input['username'] ?? '';
        $password = $input['password'] ?? '';
        $phone = $input['phone'] ?? '';
        $code = $input['code'] ?? '';
        
        if (empty($username) || empty($password) || empty($phone) || empty($code)) {
            $this->sendError('所有字段都不能为空');
            return;
        }
        
        // 验证手机验证码（这里简化处理）
        if ($code !== '123456') {
            $this->sendError('验证码错误');
            return;
        }
        
        try {
            // 这里应该将用户信息保存到数据库
            // 为演示目的，直接返回成功
            $this->sendSuccess([
                'message' => '注册成功',
                'user_id' => rand(1000, 9999)
            ]);
        } catch(Exception $e) {
            $this->sendError('注册失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 获取用户资料
     */
    public function getUserProfile() {
        $token = $this->getAuthToken();
        if (!$token) {
            $this->sendError('未授权访问', 401);
            return;
        }
        
        try {
            // 这里应该根据token查询用户信息
            $userProfile = [
                'id' => 1,
                'username' => 'demo',
                'nickname' => '演示用户',
                'avatar' => 'https://example.com/avatar.jpg',
                'phone' => '138****8888',
                'email' => '<EMAIL>',
                'account_balance' => 50000.00,
                'total_assets' => 125000.00,
                'profit_loss' => 5000.00,
                'profit_rate' => 4.17
            ];
            
            $this->sendSuccess($userProfile);
        } catch(Exception $e) {
            $this->sendError('获取用户资料失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 搜索股票
     */
    public function searchStocks() {
        $keyword = $_GET['keyword'] ?? '';
        if (empty($keyword)) {
            $this->sendError('搜索关键词不能为空');
            return;
        }
        
        try {
            // 模拟股票搜索结果
            $searchResults = [
                [
                    'code' => '000001',
                    'name' => '平安银行',
                    'price' => 12.45,
                    'change' => 0.15,
                    'percent' => 1.22
                ],
                [
                    'code' => '000002',
                    'name' => '万科A',
                    'price' => 18.76,
                    'change' => -0.32,
                    'percent' => -1.68
                ]
            ];
            
            $this->sendSuccess($searchResults);
        } catch(Exception $e) {
            $this->sendError('搜索失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 生成JWT Token
     */
    private function generateToken($username) {
        $header = json_encode(['typ' => 'JWT', 'alg' => 'HS256']);
        $payload = json_encode([
            'username' => $username,
            'exp' => time() + (24 * 60 * 60) // 24小时过期
        ]);
        
        $headerEncoded = base64url_encode($header);
        $payloadEncoded = base64url_encode($payload);
        
        $signature = hash_hmac('sha256', $headerEncoded . "." . $payloadEncoded, 'your-secret-key', true);
        $signatureEncoded = base64url_encode($signature);
        
        return $headerEncoded . "." . $payloadEncoded . "." . $signatureEncoded;
    }
    
    /**
     * 获取认证Token
     */
    private function getAuthToken() {
        $headers = getallheaders();
        $authHeader = $headers['Authorization'] ?? '';
        
        if (strpos($authHeader, 'Bearer ') === 0) {
            return substr($authHeader, 7);
        }
        
        return null;
    }
    
    /**
     * 发送成功响应
     */
    private function sendSuccess($data) {
        echo json_encode([
            'success' => true,
            'data' => $data,
            'timestamp' => time()
        ], JSON_UNESCAPED_UNICODE);
    }
    
    /**
     * 发送错误响应
     */
    private function sendError($message, $code = 400) {
        http_response_code($code);
        echo json_encode([
            'success' => false,
            'error' => $message,
            'timestamp' => time()
        ], JSON_UNESCAPED_UNICODE);
    }
}

/**
 * Base64 URL编码
 */
function base64url_encode($data) {
    return rtrim(strtr(base64_encode($data), '+/', '-_'), '=');
}

/**
 * Base64 URL解码
 */
function base64url_decode($data) {
    return base64_decode(str_pad(strtr($data, '-_', '+/'), strlen($data) % 4, '=', STR_PAD_RIGHT));
}

// 启动API
$api = new StockAPI();
$api->handleRequest();
?>
