/**
 * 导航组件
 * 负责底部导航栏的交互和页面切换
 */

class NavigationComponent {
    constructor() {
        this.currentPage = 'home';
        this.history = [];
        this.maxHistoryLength = 10;
        
        this.init();
    }

    /**
     * 初始化组件
     */
    init() {
        this.bindElements();
        this.bindEvents();
        this.initCurrentPage();
    }

    /**
     * 绑定DOM元素
     */
    bindElements() {
        this.bottomNav = document.querySelector('.bottom-nav');
        this.navItems = document.querySelectorAll('.nav-item');
        this.mainContent = document.querySelector('.main-content');
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // 底部导航点击事件
        this.navItems.forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                const page = item.dataset.page;
                if (page && page !== this.currentPage) {
                    this.navigateTo(page);
                }
            });
        });

        // 浏览器前进后退事件
        window.addEventListener('popstate', (e) => {
            if (e.state && e.state.page) {
                this.setActivePage(e.state.page, false);
            }
        });

        // 页面可见性变化事件
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden) {
                this.onPageVisible();
            }
        });
    }

    /**
     * 初始化当前页面
     */
    initCurrentPage() {
        // 从URL参数或默认值确定当前页面
        const urlParams = Utils.url.getParams();
        const page = urlParams.page || this.getCurrentPageFromPath() || 'home';
        
        this.setActivePage(page, false);
    }

    /**
     * 从路径获取当前页面
     */
    getCurrentPageFromPath() {
        const path = window.location.pathname;
        const pageMap = {
            '/': 'home',
            '/index.html': 'home',
            '/pages/market.html': 'market',
            '/pages/investment.html': 'investment',
            '/pages/profile.html': 'profile'
        };
        
        return pageMap[path] || null;
    }

    /**
     * 导航到指定页面
     */
    navigateTo(page, addToHistory = true) {
        if (!this.isValidPage(page)) {
            console.error(`Invalid page: ${page}`);
            return;
        }

        // 检查是否需要登录
        if (this.requiresAuth(page) && !this.isUserLoggedIn()) {
            this.redirectToLogin();
            return;
        }

        // 添加到历史记录
        if (addToHistory) {
            this.addToHistory(this.currentPage);
            
            // 更新浏览器历史
            const pageConfig = this.getPageConfig(page);
            const state = { page: page };
            const title = `${pageConfig.name} - ${Constants.APP_CONFIG.NAME}`;
            const url = pageConfig.url;
            
            window.history.pushState(state, title, url);
        }

        this.setActivePage(page);
    }

    /**
     * 设置活动页面
     */
    setActivePage(page, updateContent = true) {
        if (!this.isValidPage(page)) {
            return;
        }

        const previousPage = this.currentPage;
        this.currentPage = page;

        // 更新导航栏状态
        this.updateNavigation();

        // 更新页面内容
        if (updateContent) {
            this.updatePageContent(page, previousPage);
        }

        // 触发页面变化事件
        this.onPageChanged(page, previousPage);
    }

    /**
     * 更新导航栏状态
     */
    updateNavigation() {
        this.navItems.forEach(item => {
            const page = item.dataset.page;
            if (page === this.currentPage) {
                item.classList.add('active');
            } else {
                item.classList.remove('active');
            }
        });
    }

    /**
     * 更新页面内容
     */
    async updatePageContent(page, previousPage) {
        try {
            // 添加加载状态
            this.mainContent.classList.add('loading');

            // 根据页面类型加载内容
            switch (page) {
                case 'home':
                    await this.loadHomePage();
                    break;
                case 'market':
                    await this.loadMarketPage();
                    break;
                case 'investment':
                    await this.loadInvestmentPage();
                    break;
                case 'profile':
                    await this.loadProfilePage();
                    break;
                default:
                    console.warn(`Unknown page: ${page}`);
            }

            // 移除加载状态
            this.mainContent.classList.remove('loading');

            // 添加页面切换动画
            this.addPageTransition(page, previousPage);

        } catch (error) {
            console.error(`Failed to load page ${page}:`, error);
            this.showError('页面加载失败');
            this.mainContent.classList.remove('loading');
        }
    }

    /**
     * 加载首页内容
     */
    async loadHomePage() {
        // 首页内容已经在HTML中，只需要刷新数据
        if (window.stockDataComponent) {
            await window.stockDataComponent.refresh();
        }
    }

    /**
     * 加载行情页面
     */
    async loadMarketPage() {
        // 这里可以动态加载行情页面内容
        console.log('Loading market page...');
        // 实际实现中可以通过AJAX加载页面内容
    }

    /**
     * 加载智投页面
     */
    async loadInvestmentPage() {
        console.log('Loading investment page...');
    }

    /**
     * 加载个人页面
     */
    async loadProfilePage() {
        console.log('Loading profile page...');
    }

    /**
     * 添加页面切换动画
     */
    addPageTransition(newPage, oldPage) {
        if (!oldPage || newPage === oldPage) {
            return;
        }

        // 添加切换动画类
        this.mainContent.classList.add('page-transition');

        // 动画完成后移除类
        setTimeout(() => {
            this.mainContent.classList.remove('page-transition');
        }, Constants.APP_CONFIG.ANIMATION_DURATION);
    }

    /**
     * 检查页面是否有效
     */
    isValidPage(page) {
        return Constants.NAVIGATION_CONFIG.PAGES.some(p => p.id === page);
    }

    /**
     * 检查页面是否需要登录
     */
    requiresAuth(page) {
        const authRequiredPages = ['investment', 'profile'];
        return authRequiredPages.includes(page);
    }

    /**
     * 检查用户是否已登录
     */
    isUserLoggedIn() {
        const token = Utils.storage.get(Constants.STORAGE_KEYS.USER_TOKEN);
        return !!token;
    }

    /**
     * 重定向到登录页面
     */
    redirectToLogin() {
        // 保存当前页面，登录后返回
        Utils.storage.set('returnUrl', window.location.href);
        
        if (window.Toast) {
            Toast.show('请先登录', Constants.MESSAGE_TYPE.INFO);
        }
        
        setTimeout(() => {
            window.location.href = '/src/pages/login.html';
        }, 1000);
    }

    /**
     * 获取页面配置
     */
    getPageConfig(page) {
        return Constants.NAVIGATION_CONFIG.PAGES.find(p => p.id === page);
    }

    /**
     * 添加到历史记录
     */
    addToHistory(page) {
        this.history.push(page);
        
        // 限制历史记录长度
        if (this.history.length > this.maxHistoryLength) {
            this.history.shift();
        }
    }

    /**
     * 返回上一页
     */
    goBack() {
        if (this.history.length > 0) {
            const previousPage = this.history.pop();
            this.navigateTo(previousPage, false);
            window.history.back();
        } else {
            // 如果没有历史记录，返回首页
            this.navigateTo('home');
        }
    }

    /**
     * 页面变化事件处理
     */
    onPageChanged(newPage, oldPage) {
        // 更新页面标题
        const pageConfig = this.getPageConfig(newPage);
        if (pageConfig) {
            document.title = `${pageConfig.name} - ${Constants.APP_CONFIG.NAME}`;
        }

        // 触发自定义事件
        const event = new CustomEvent('pageChanged', {
            detail: { newPage, oldPage }
        });
        document.dispatchEvent(event);

        // 页面统计
        this.trackPageView(newPage);
    }

    /**
     * 页面可见时的处理
     */
    onPageVisible() {
        // 页面重新可见时刷新数据
        if (this.currentPage === 'home' && window.stockDataComponent) {
            window.stockDataComponent.refresh();
        }
    }

    /**
     * 页面访问统计
     */
    trackPageView(page) {
        // 这里可以添加页面访问统计逻辑
        if (Constants.APP_CONFIG.DEBUG) {
            console.log(`Page view: ${page}`);
        }
    }

    /**
     * 显示错误信息
     */
    showError(message) {
        if (window.Toast) {
            Toast.show(message, Constants.MESSAGE_TYPE.ERROR);
        } else {
            console.error(message);
        }
    }

    /**
     * 获取当前页面
     */
    getCurrentPage() {
        return this.currentPage;
    }

    /**
     * 获取历史记录
     */
    getHistory() {
        return [...this.history];
    }

    /**
     * 销毁组件
     */
    destroy() {
        // 移除事件监听器
        this.navItems.forEach(item => {
            item.removeEventListener('click', this.handleNavClick);
        });
        
        window.removeEventListener('popstate', this.handlePopState);
        document.removeEventListener('visibilitychange', this.handleVisibilityChange);
    }
}

// 导出组件
if (typeof module !== 'undefined' && module.exports) {
    module.exports = NavigationComponent;
} else {
    window.NavigationComponent = NavigationComponent;
}
