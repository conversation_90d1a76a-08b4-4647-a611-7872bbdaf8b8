/**
 * 新闻组件
 * 负责获取和显示华尔街见闻的新闻数据
 */
class NewsComponent {
    constructor() {
        this.newsContainer = null;
        this.moreButton = null;
        this.newsData = [];
        this.isLoading = false;
        this.maxRetries = 3;
        this.retryCount = 0;

        this.init();
    }

    /**
     * 初始化组件
     */
    init() {
        this.newsContainer = document.querySelector('.news-list');
        this.moreButton = document.querySelector('.more-link');

        if (!this.newsContainer) {
            console.error('News container not found');
            return;
        }

        this.bindEvents();
        this.loadNews();
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        if (this.moreButton) {
            this.moreButton.addEventListener('click', () => {
                window.open('http://m.wallstreetnews.com.cn/', '_blank');
            });
        }
    }

    /**
     * 加载新闻数据
     */
    async loadNews() {
        if (this.isLoading) return;

        this.isLoading = true;
        this.showLoading();

        try {
            // 由于跨域限制，我们使用模拟数据来展示新闻格式
            // 在实际部署时，需要通过后端代理来获取真实数据
            const newsData = await this.fetchNewsData();
            this.newsData = newsData;
            this.renderNews();
            this.retryCount = 0;
        } catch (error) {
            console.error('Failed to load news:', error);
            this.handleError();
        } finally {
            this.isLoading = false;
        }
    }

    /**
     * 获取新闻数据
     * 注意：由于跨域限制，这里使用模拟数据
     * 实际应用中需要通过后端API代理获取
     */
    async fetchNewsData() {
        // 模拟华尔街见闻风格的新闻数据
        return new Promise((resolve) => {
            setTimeout(() => {
                resolve([
                    {
                        id: 1,
                        title: "美股三大指数集体收涨，纳指涨超1%",
                        summary: "科技股领涨，特斯拉涨超3%，苹果、微软等权重股均有不错表现",
                        image: "https://images.unsplash.com/photo-1611974789855-9c2a0a7236a3?w=200&h=120&fit=crop",
                        publishTime: "2小时前",
                        source: "华尔街见闻",
                        url: "http://m.wallstreetnews.com.cn/"
                    },
                    {
                        id: 2,
                        title: "央行今日进行1000亿元逆回购操作",
                        summary: "维护银行体系流动性合理充裕，7天期逆回购利率维持1.8%不变",
                        image: "https://images.unsplash.com/photo-1559526324-4b87b5e36e44?w=200&h=120&fit=crop",
                        publishTime: "4小时前",
                        source: "华尔街见闻",
                        url: "http://m.wallstreetnews.com.cn/"
                    },
                    {
                        id: 3,
                        title: "A股三大指数午后走强，创业板指涨超1%",
                        summary: "新能源汽车板块领涨，比亚迪、宁德时代等龙头股表现强势",
                        image: "https://images.unsplash.com/photo-1590283603385-17ffb3a7f29f?w=200&h=120&fit=crop",
                        publishTime: "6小时前",
                        source: "华尔街见闻",
                        url: "http://m.wallstreetnews.com.cn/"
                    }
                ]);
            }, 1000);
        });
    }

    /**
     * 渲染新闻列表
     */
    renderNews() {
        if (!this.newsData || this.newsData.length === 0) {
            this.showEmptyState();
            return;
        }

        const newsHTML = this.newsData.map(news => this.createNewsItem(news)).join('');
        this.newsContainer.innerHTML = newsHTML;

        // 绑定新闻项点击事件
        this.bindNewsItemEvents();
    }

    /**
     * 绑定新闻项事件
     */
    bindNewsItemEvents() {
        const newsItems = this.newsContainer.querySelectorAll('.news-item');
        newsItems.forEach(item => {
            item.addEventListener('click', () => {
                const newsId = item.dataset.newsId;
                const newsData = this.newsData.find(news => news.id == newsId);
                if (newsData) {
                    this.openNewsDetail(newsData);
                }
            });
        });
    }

    /**
     * 打开新闻详情
     */
    openNewsDetail(newsData) {
        // 跳转到华尔街见闻
        window.open(newsData.url, '_blank');

        // 可以在这里添加统计代码
        if (window.guosenApp && Constants.APP_CONFIG.DEBUG) {
            console.log('News clicked:', newsData.title);
        }
    }

    /**
     * 创建单个新闻项
     */
    createNewsItem(news) {
        return `
            <div class="news-item" data-news-id="${news.id}">
                <div class="news-content">
                    <h4 class="news-title">${news.title}</h4>
                    <p class="news-summary">${news.summary}</p>
                    <div class="news-meta">
                        <span class="news-source">${news.source}</span>
                        <span class="news-time">${news.publishTime}</span>
                    </div>
                </div>
                <div class="news-image">
                    <img src="${news.image}" alt="${news.title}" onerror="this.style.display='none'">
                </div>
            </div>
        `;
    }

    /**
     * 显示加载状态
     */
    showLoading() {
        this.newsContainer.innerHTML = `
            <div class="news-loading">
                <i class="fas fa-spinner fa-spin"></i>
                <span>正在加载新闻...</span>
            </div>
        `;
    }

    /**
     * 显示空状态
     */
    showEmptyState() {
        this.newsContainer.innerHTML = `
            <div class="news-empty">
                <i class="fas fa-newspaper"></i>
                <span>暂无新闻数据</span>
            </div>
        `;
    }

    /**
     * 处理错误
     */
    handleError() {
        if (this.retryCount < this.maxRetries) {
            this.retryCount++;
            setTimeout(() => this.loadNews(), 2000 * this.retryCount);
        } else {
            this.newsContainer.innerHTML = `
                <div class="news-error">
                    <i class="fas fa-exclamation-triangle"></i>
                    <span>加载失败，请稍后重试</span>
                    <button class="retry-btn" onclick="window.newsComponent.loadNews()">重试</button>
                </div>
            `;
        }
    }

    /**
     * 刷新新闻
     */
    refresh() {
        this.retryCount = 0;
        this.loadNews();
    }

    /**
     * 销毁组件
     */
    destroy() {
        if (this.moreButton) {
            this.moreButton.removeEventListener('click', this.handleMoreClick);
        }
        this.newsData = [];
    }
}

// 导出组件
if (typeof module !== 'undefined' && module.exports) {
    module.exports = NewsComponent;
} else {
    window.NewsComponent = NewsComponent;
}
