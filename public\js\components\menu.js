/**
 * 功能菜单组件
 * 负责处理功能菜单的点击事件和页面跳转
 */

class MenuComponent {
    constructor() {
        this.menuItems = new Map();
        this.clickHandlers = new Map();
        
        this.init();
    }

    /**
     * 初始化组件
     */
    init() {
        this.bindElements();
        this.bindEvents();
        this.loadMenuConfig();
    }

    /**
     * 绑定DOM元素
     */
    bindElements() {
        this.functionMenu = document.querySelector('.function-menu');
        this.menuItemElements = document.querySelectorAll('.menu-item');
        this.openAccountBtn = document.querySelector('.open-account-btn');
        this.loginRegisterBtn = document.querySelector('.login-register');
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // 功能菜单项点击事件
        this.menuItemElements.forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                const action = item.dataset.action;
                if (action) {
                    this.handleMenuClick(action, item);
                }
            });
        });

        // 开户按钮点击事件
        if (this.openAccountBtn) {
            this.openAccountBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.handleOpenAccount();
            });
        }

        // 登录注册按钮点击事件
        if (this.loginRegisterBtn) {
            this.loginRegisterBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.handleLoginRegister();
            });
        }

        // 搜索按钮点击事件
        const searchBtn = document.querySelector('.header-right .fa-search');
        if (searchBtn) {
            searchBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.handleSearch();
            });
        }

        // 更多新闻点击事件
        const moreNewsBtn = document.querySelector('.more-link');
        if (moreNewsBtn) {
            moreNewsBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.handleMoreNews();
            });
        }
    }

    /**
     * 加载菜单配置
     */
    loadMenuConfig() {
        Constants.MENU_CONFIG.ITEMS.forEach(item => {
            this.menuItems.set(item.id, item);
        });
    }

    /**
     * 处理菜单点击
     */
    async handleMenuClick(action, element) {
        const menuConfig = this.menuItems.get(action);
        
        if (!menuConfig) {
            console.warn(`Unknown menu action: ${action}`);
            return;
        }

        // 添加点击效果
        this.addClickEffect(element);

        // 检查是否需要登录
        if (menuConfig.requireAuth && !this.isUserLoggedIn()) {
            this.showLoginRequired(menuConfig.name);
            return;
        }

        // 显示加载提示
        this.showToast(`正在打开${menuConfig.name}...`, Constants.MESSAGE_TYPE.INFO);

        try {
            // 根据不同的功能执行不同的操作
            switch (action) {
                case 'smart-investment':
                    await this.handleSmartInvestment();
                    break;
                case 'golden-stocks':
                    await this.handleGoldenStocks();
                    break;
                case 'activity-center':
                    await this.handleActivityCenter();
                    break;
                case 'my-stocks':
                    await this.handleMyStocks();
                    break;
                case 'deposit-withdraw':
                    await this.handleDepositWithdraw();
                    break;
                case 'reverse-commission':
                    await this.handleReverseCommission();
                    break;
                case 'my-positions':
                    await this.handleMyPositions();
                    break;
                case 'company-info':
                    await this.handleCompanyInfo();
                    break;
                case 'customer-service':
                    await this.handleCustomerService();
                    break;
                case 'app-download':
                    await this.handleAppDownload();
                    break;
                default:
                    this.handleGenericMenu(menuConfig);
            }
        } catch (error) {
            console.error(`Error handling menu action ${action}:`, error);
            this.showToast('操作失败，请稍后重试', Constants.MESSAGE_TYPE.ERROR);
        }
    }

    /**
     * 智能跟投
     */
    async handleSmartInvestment() {
        // 模拟加载时间
        await this.delay(1000);
        this.showToast('智能跟投功能开发中...', Constants.MESSAGE_TYPE.INFO);
    }

    /**
     * 指标金股
     */
    async handleGoldenStocks() {
        await this.delay(1000);
        this.showToast('指标金股功能开发中...', Constants.MESSAGE_TYPE.INFO);
    }

    /**
     * 活动中心
     */
    async handleActivityCenter() {
        await this.delay(1000);
        this.showToast('活动中心功能开发中...', Constants.MESSAGE_TYPE.INFO);
    }

    /**
     * 我的自选
     */
    async handleMyStocks() {
        await this.delay(1000);
        this.showToast('我的自选功能开发中...', Constants.MESSAGE_TYPE.INFO);
    }

    /**
     * 充值提现
     */
    async handleDepositWithdraw() {
        await this.delay(1000);
        this.showToast('充值提现功能开发中...', Constants.MESSAGE_TYPE.INFO);
    }

    /**
     * 逆向返佣
     */
    async handleReverseCommission() {
        await this.delay(1000);
        this.showToast('逆向返佣功能开发中...', Constants.MESSAGE_TYPE.INFO);
    }

    /**
     * 我的持仓
     */
    async handleMyPositions() {
        await this.delay(1000);
        this.showToast('我的持仓功能开发中...', Constants.MESSAGE_TYPE.INFO);
    }

    /**
     * 公司简介
     */
    async handleCompanyInfo() {
        await this.delay(500);
        this.showCompanyInfo();
    }

    /**
     * 在线客服
     */
    async handleCustomerService() {
        await this.delay(500);
        this.showCustomerService();
    }

    /**
     * APP下载
     */
    async handleAppDownload() {
        await this.delay(500);
        this.showAppDownload();
    }

    /**
     * 处理开户
     */
    async handleOpenAccount() {
        this.showToast('正在跳转到开户页面...', Constants.MESSAGE_TYPE.INFO);
        
        await this.delay(1000);
        
        // 这里可以跳转到实际的开户页面
        this.showToast('开户功能开发中...', Constants.MESSAGE_TYPE.INFO);
    }

    /**
     * 处理登录注册
     */
    async handleLoginRegister() {
        this.showToast('正在跳转到登录页面...', Constants.MESSAGE_TYPE.INFO);
        
        await this.delay(1000);
        
        // 跳转到登录页面
        window.location.href = '/src/pages/login.html';
    }

    /**
     * 处理搜索
     */
    async handleSearch() {
        const searchTerm = prompt('请输入股票代码或名称:');
        if (searchTerm && searchTerm.trim()) {
            this.showToast(`正在搜索: ${searchTerm}`, Constants.MESSAGE_TYPE.INFO);
            await this.searchStock(searchTerm.trim());
        }
    }

    /**
     * 处理更多新闻
     */
    async handleMoreNews() {
        this.showToast('正在加载更多新闻...', Constants.MESSAGE_TYPE.INFO);
        
        await this.delay(1000);
        
        this.showToast('新闻列表功能开发中...', Constants.MESSAGE_TYPE.INFO);
    }

    /**
     * 通用菜单处理
     */
    handleGenericMenu(menuConfig) {
        if (menuConfig.url) {
            window.location.href = menuConfig.url;
        } else {
            this.showToast(`${menuConfig.name}功能开发中...`, Constants.MESSAGE_TYPE.INFO);
        }
    }

    /**
     * 搜索股票
     */
    async searchStock(keyword) {
        try {
            const response = await fetch(`${Constants.API_CONFIG.BASE_URL}${Constants.API_CONFIG.ENDPOINTS.SEARCH}?keyword=${encodeURIComponent(keyword)}`);
            const result = await response.json();
            
            if (result.success && result.data.length > 0) {
                this.showSearchResults(result.data);
            } else {
                this.showToast('未找到相关股票', Constants.MESSAGE_TYPE.WARNING);
            }
        } catch (error) {
            console.error('Search error:', error);
            this.showToast('搜索失败，请稍后重试', Constants.MESSAGE_TYPE.ERROR);
        }
    }

    /**
     * 显示搜索结果
     */
    showSearchResults(results) {
        // 这里可以显示搜索结果弹窗或跳转到搜索结果页面
        console.log('Search results:', results);
        this.showToast(`找到 ${results.length} 个相关结果`, Constants.MESSAGE_TYPE.SUCCESS);
    }

    /**
     * 显示公司简介
     */
    showCompanyInfo() {
        const info = `
            国信证券股份有限公司成立于1994年，是中国证券业协会理事单位，
            深圳证券交易所会员单位，具有证券经纪、证券投资咨询、
            证券承销与保荐等业务资格。
        `;
        
        alert(info); // 实际项目中应该使用模态框
    }

    /**
     * 显示客服信息
     */
    showCustomerService() {
        const serviceInfo = `
            客服热线：400-8888-999
            在线时间：周一至周五 9:00-17:00
            微信客服：guosen_service
        `;
        
        alert(serviceInfo); // 实际项目中应该使用模态框
    }

    /**
     * 显示APP下载信息
     */
    showAppDownload() {
        const downloadInfo = `
            国信证券APP下载：
            
            iOS版本：App Store搜索"国信证券"
            Android版本：官网下载或应用商店搜索
            
            扫描二维码下载最新版本
        `;
        
        alert(downloadInfo); // 实际项目中应该使用模态框
    }

    /**
     * 添加点击效果
     */
    addClickEffect(element) {
        element.classList.add('clicked');
        
        setTimeout(() => {
            element.classList.remove('clicked');
        }, 200);
    }

    /**
     * 检查用户是否已登录
     */
    isUserLoggedIn() {
        const token = Utils.storage.get(Constants.STORAGE_KEYS.USER_TOKEN);
        return !!token;
    }

    /**
     * 显示需要登录的提示
     */
    showLoginRequired(featureName) {
        this.showToast(`使用${featureName}功能需要先登录`, Constants.MESSAGE_TYPE.WARNING);
        
        setTimeout(() => {
            if (confirm('是否前往登录页面？')) {
                this.handleLoginRegister();
            }
        }, 1500);
    }

    /**
     * 显示提示信息
     */
    showToast(message, type = Constants.MESSAGE_TYPE.INFO) {
        if (window.Toast) {
            Toast.show(message, type);
        } else {
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
    }

    /**
     * 延迟函数
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 获取菜单配置
     */
    getMenuConfig(action) {
        return this.menuItems.get(action);
    }

    /**
     * 销毁组件
     */
    destroy() {
        this.menuItems.clear();
        this.clickHandlers.clear();
    }
}

// 导出组件
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MenuComponent;
} else {
    window.MenuComponent = MenuComponent;
}
