/**
 * 登录页面脚本
 */

class LoginPage {
    constructor() {
        this.form = document.getElementById('loginForm');
        this.usernameInput = document.getElementById('username');
        this.passwordInput = document.getElementById('password');
        this.passwordToggle = document.getElementById('passwordToggle');
        this.loginBtn = document.getElementById('loginBtn');
        this.rememberMe = document.getElementById('rememberMe');

        this.isLoading = false;

        this.init();
    }

    init() {
        this.bindEvents();
        this.loadRememberedUser();
        this.checkAutoLogin();
    }

    bindEvents() {
        // 表单提交
        this.form.addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleLogin();
        });

        // 实时验证
        this.usernameInput.addEventListener('blur', () => {
            this.validateUsername();
        });

        this.usernameInput.addEventListener('input', () => {
            this.clearError('username');
        });

        this.passwordInput.addEventListener('blur', () => {
            this.validatePassword();
        });

        this.passwordInput.addEventListener('input', () => {
            this.clearError('password');
        });

        // 密码显示/隐藏切换
        this.passwordToggle.addEventListener('click', () => {
            this.togglePasswordVisibility();
        });

        // 回车登录
        this.passwordInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !this.isLoading) {
                this.handleLogin();
            }
        });

        // 记住我状态变化
        this.rememberMe.addEventListener('change', () => {
            this.onRememberMeChanged();
        });
    }

    validateUsername() {
        const username = this.usernameInput.value.trim();

        if (!username) {
            this.showError('username', '用户名不能为空');
            return false;
        }

        if (username.length < 3) {
            this.showError('username', '用户名长度至少3位');
            return false;
        }

        this.clearError('username');
        return true;
    }

    validatePassword() {
        const password = this.passwordInput.value;

        if (!password) {
            this.showError('password', '密码不能为空');
            return false;
        }

        if (password.length < 6) {
            this.showError('password', '密码长度至少6位');
            return false;
        }

        this.clearError('password');
        return true;
    }

    showError(field, message) {
        const input = document.getElementById(field);
        const error = document.getElementById(field + 'Error');

        input.classList.add('error');
        error.textContent = message;
        error.style.display = 'block';
    }

    clearError(field) {
        const input = document.getElementById(field);
        const error = document.getElementById(field + 'Error');

        input.classList.remove('error');
        error.style.display = 'none';
    }

    togglePasswordVisibility() {
        const isPassword = this.passwordInput.type === 'password';
        const icon = this.passwordToggle.querySelector('i');

        this.passwordInput.type = isPassword ? 'text' : 'password';
        icon.className = isPassword ? 'fas fa-eye-slash' : 'fas fa-eye';
    }

    async handleLogin() {
        if (this.isLoading) return;

        // 验证表单
        const isUsernameValid = this.validateUsername();
        const isPasswordValid = this.validatePassword();

        if (!isUsernameValid || !isPasswordValid) {
            this.showToast('请检查输入信息', 'error');
            return;
        }

        const username = this.usernameInput.value.trim();
        const password = this.passwordInput.value;

        this.setLoading(true);

        try {
            // 模拟登录API调用
            const result = await this.simulateLogin(username, password);

            if (result.success) {
                // 保存登录状态
                this.saveLoginState(result.data);

                // 记住用户
                this.handleRememberUser(username);

                this.showToast('登录成功！', 'success');

                // 跳转到目标页面
                setTimeout(() => {
                    this.redirectAfterLogin();
                }, 1000);

            } else {
                this.showToast(result.message || '登录失败', 'error');

                // 如果是密码错误，清空密码输入框
                if (result.message && result.message.includes('密码')) {
                    this.passwordInput.value = '';
                    this.passwordInput.focus();
                }
            }
        } catch (error) {
            console.error('登录错误:', error);
            this.showToast('网络错误，请稍后重试', 'error');
        } finally {
            this.setLoading(false);
        }
    }

    /**
     * 模拟登录API
     */
    async simulateLogin(username, password) {
        return new Promise((resolve) => {
            setTimeout(() => {
                // 简单的模拟验证
                if (username && password.length >= 6) {
                    resolve({
                        success: true,
                        data: {
                            token: 'mock_token_' + Date.now(),
                            user: {
                                id: 1,
                                name: username,
                                phone: username.includes('@') ? '' : username,
                                email: username.includes('@') ? username : '',
                                avatar: ''
                            }
                        }
                    });
                } else {
                    resolve({
                        success: false,
                        message: '用户名或密码错误'
                    });
                }
            }, 1500); // 模拟网络延迟
        });
    }

    saveLoginState(data) {
        // 保存Token和用户信息
        localStorage.setItem('userToken', data.token);
        localStorage.setItem('userInfo', JSON.stringify(data.user));

        // 保存登录时间
        localStorage.setItem('loginTime', Date.now().toString());
    }

    handleRememberUser(username) {
        if (this.rememberMe.checked) {
            Utils.storage.set(Constants.STORAGE_KEYS.REMEMBERED_USER, username);
        } else {
            Utils.storage.remove(Constants.STORAGE_KEYS.REMEMBERED_USER);
        }
    }

    loadRememberedUser() {
        const rememberedUser = Utils.storage.get(Constants.STORAGE_KEYS.REMEMBERED_USER);
        if (rememberedUser) {
            this.usernameInput.value = rememberedUser;
            this.rememberMe.checked = true;
            this.passwordInput.focus();
        } else {
            this.usernameInput.focus();
        }
    }

    checkAutoLogin() {
        // 检查是否有有效的登录状态
        const token = Utils.storage.get(Constants.STORAGE_KEYS.USER_TOKEN);
        const userInfo = Utils.storage.get(Constants.STORAGE_KEYS.USER_INFO);

        if (token && userInfo) {
            // 验证Token是否仍然有效
            this.validateExistingToken(token);
        }
    }

    async validateExistingToken(token) {
        try {
            const response = await fetch('/backend/api/auth.php?action=validate', {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });

            const result = await response.json();

            if (result.success) {
                // Token仍然有效，直接跳转
                this.showToast('自动登录成功', 'success');
                setTimeout(() => {
                    this.redirectAfterLogin();
                }, 1000);
            } else {
                // Token已失效，清除本地存储
                this.clearLoginState();
            }
        } catch (error) {
            console.error('Token验证错误:', error);
            this.clearLoginState();
        }
    }

    clearLoginState() {
        Utils.storage.remove(Constants.STORAGE_KEYS.USER_TOKEN);
        Utils.storage.remove(Constants.STORAGE_KEYS.USER_INFO);
    }

    redirectAfterLogin() {
        // 获取返回URL
        const returnUrl = localStorage.getItem('returnUrl') || '../home/<USER>';
        localStorage.removeItem('returnUrl');

        window.location.href = returnUrl;
    }

    onRememberMeChanged() {
        const username = this.usernameInput.value.trim();

        if (!this.rememberMe.checked && username) {
            // 如果取消记住我，且当前用户名是记住的用户，则清除
            const rememberedUser = Utils.storage.get(Constants.STORAGE_KEYS.REMEMBERED_USER);
            if (username === rememberedUser) {
                Utils.storage.remove(Constants.STORAGE_KEYS.REMEMBERED_USER);
            }
        }
    }

    setLoading(loading) {
        this.isLoading = loading;
        this.loginBtn.disabled = loading;

        const btnText = this.loginBtn.querySelector('.btn-text');
        const btnLoading = this.loginBtn.querySelector('.btn-loading');

        if (loading) {
            btnText.classList.add('hidden');
            btnLoading.classList.remove('hidden');
        } else {
            btnText.classList.remove('hidden');
            btnLoading.classList.add('hidden');
        }
    }

    showToast(message, type = 'info', duration = 3000) {
        // 移除现有的toast
        const existingToast = document.querySelector('.toast');
        if (existingToast) {
            existingToast.remove();
        }

        // 创建新的toast
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        toast.textContent = message;

        document.body.appendChild(toast);

        // 自动移除
        setTimeout(() => {
            if (toast.parentNode) {
                toast.style.animation = 'slideUp 0.3s ease';
                setTimeout(() => {
                    if (toast.parentNode) {
                        toast.remove();
                    }
                }, 300);
            }
        }, duration);
    }

    // 获取表单数据
    getFormData() {
        return {
            username: this.usernameInput.value.trim(),
            password: this.passwordInput.value,
            rememberMe: this.rememberMe.checked
        };
    }

    // 重置表单
    resetForm() {
        this.form.reset();
        this.clearError('username');
        this.clearError('password');
        this.passwordInput.type = 'password';
        this.passwordToggle.querySelector('i').className = 'fas fa-eye';
    }
}

// 忘记密码功能
function handleForgotPassword() {
    showToast('忘记密码功能开发中...', 'info');
}

// 全局Toast函数
function showToast(message, type = 'info', duration = 3000) {
    if (window.loginPage) {
        window.loginPage.showToast(message, type, duration);
    } else {
        console.log(`[${type.toUpperCase()}] ${message}`);
    }
}

// 添加动画样式
const style = document.createElement('style');
style.textContent = `
    @keyframes slideUp {
        from {
            opacity: 1;
            transform: translateX(-50%) translateY(0);
        }
        to {
            opacity: 0;
            transform: translateX(-50%) translateY(-20px);
        }
    }
`;
document.head.appendChild(style);

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    window.loginPage = new LoginPage();
});

// 页面卸载时清理
window.addEventListener('beforeunload', () => {
    if (window.loginPage) {
        // 可以在这里添加清理逻辑
    }
});
