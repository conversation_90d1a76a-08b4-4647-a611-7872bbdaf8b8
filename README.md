# 国信证券移动端应用

一个现代化的移动端证券交易应用，提供股票行情、智能投资、用户管理等功能。

## 📁 项目文件夹结构

```
zhengquan/
├── index.html                          # 入口文件（重定向到首页模块）
├── README.md                           # 项目说明文档
├── backend/                            # 后端相关文件
│   ├── api/                           # API接口
│   │   ├── auth.php                   # 用户认证API
│   │   └── stock-data.php             # 股票数据API
│   ├── config/                        # 配置文件
│   │   └── database.php               # 数据库配置
│   └── utils/                         # 后端工具类
│       ├── auth.php                   # 认证工具
│       └── response.php               # 响应工具

├── pages/                             # 页面文件
│   ├── home/                          # 首页模块
│   │   └── index.html                 # 首页主页面
│   ├── market/                        # 行情模块
│   │   └── index.html                 # 行情主页面
│   ├── smart-invest/                  # 智投模块
│   │   └── index.html                 # 智投主页面
│   ├── profile/                       # 我的模块
│   │   └── index.html                 # 个人中心主页面
│   ├── login.html                     # 登录页面
│   └── register.html                  # 注册页面
└── public/                            # 静态资源
    ├── css/                           # 样式文件
    │   ├── home/                      # 首页相关样式（预留）
    │   ├── market/                    # 行情相关样式（预留）
    │   ├── smart-invest/              # 智投相关样式（预留）
    │   ├── profile/                   # 我的相关样式（预留）
    │   ├── main.css                   # 全局主样式
    │   ├── components.css             # 组件样式
    │   └── login.css                  # 登录注册页面样式
    ├── fonts/                         # 字体文件（预留）
    ├── images/                        # 图片资源
    │   └── placeholder.svg            # 占位图片
    └── js/                            # JavaScript文件
        ├── home/                      # 首页相关脚本
        │   └── auth-handler.js        # 认证处理器
        ├── market/                    # 行情相关脚本（预留）
        ├── smart-invest/              # 智投相关脚本（预留）
        ├── profile/                   # 我的相关脚本（预留）
        ├── components/                # 组件脚本
        │   ├── menu.js                # 菜单组件
        │   ├── navigation.js          # 导航组件
        │   ├── newsComponent.js       # 新闻组件
        │   └── stockData.js           # 股票数据组件
        ├── utils/                     # 工具脚本
        │   ├── constants.js           # 常量定义
        │   ├── helpers.js             # 辅助函数
        │   └── navigation.js          # 导航工具类
        ├── app.js                     # 主应用脚本
        ├── login.js                   # 登录页面脚本
        └── register.js                # 注册页面脚本
```