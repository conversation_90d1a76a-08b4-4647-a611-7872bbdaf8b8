# 国信证券移动端应用

## 📁 项目文件夹结构

```
zhengquan/
├── index.html                          # 入口文件（重定向到首页模块）
├── README.md                           # 项目说明文档
├── backend/                            # 后端相关文件
│   ├── api/                           # API接口
│   │   ├── auth.php                   # 用户认证API
│   │   └── stock-data.php             # 股票数据API
│   ├── config/                        # 配置文件
│   │   └── database.php               # 数据库配置
│   └── utils/                         # 后端工具类
│       ├── auth.php                   # 认证工具
│       └── response.php               # 响应工具
├── docs/                              # 文档目录（空）
├── pages/                             # 页面文件
│   ├── home/                          # 首页模块
│   │   └── index.html                 # 首页主页面
│   ├── market/                        # 行情模块
│   │   └── index.html                 # 行情主页面
│   ├── smart-invest/                  # 智投模块
│   │   └── index.html                 # 智投主页面
│   ├── profile/                       # 我的模块
│   │   └── index.html                 # 个人中心主页面
│   ├── login.html                     # 登录页面
│   └── register.html                  # 注册页面
└── public/                            # 静态资源
    ├── css/                           # 样式文件
    │   ├── home/                      # 首页相关样式（待添加）
    │   ├── market/                    # 行情相关样式（待添加）
    │   ├── smart-invest/              # 智投相关样式（待添加）
    │   ├── profile/                   # 我的相关样式（待添加）
    │   ├── main.css                   # 全局主样式
    │   ├── components.css             # 组件样式
    │   └── login.css                  # 登录页面样式
    ├── fonts/                         # 字体文件（空）
    ├── images/                        # 图片资源
    │   └── placeholder.svg            # 占位图片
    └── js/                            # JavaScript文件
        ├── home/                      # 首页相关脚本
        │   └── auth-handler.js        # 认证处理器
        ├── market/                    # 行情相关脚本（待添加）
        ├── smart-invest/              # 智投相关脚本（待添加）
        ├── profile/                   # 我的相关脚本（待添加）
        ├── components/                # 组件脚本
        │   ├── menu.js                # 菜单组件
        │   ├── navigation.js          # 导航组件
        │   ├── newsComponent.js       # 新闻组件
        │   └── stockData.js           # 股票数据组件
        ├── utils/                     # 工具脚本
        │   ├── constants.js           # 常量定义
        │   └── helpers.js             # 辅助函数
        ├── app.js                     # 主应用脚本
        ├── login.js                   # 登录页面脚本
        └── register.js                # 注册页面脚本
```

## 🎯 模块说明

### **主要模块**
- **首页模块** (`pages/home/<USER>
- **行情模块** (`pages/market/`) - 股票行情数据展示（开发中）
- **智投模块** (`pages/smart-invest/`) - AI智能投资服务（开发中）
- **我的模块** (`pages/profile/`) - 个人中心和账户管理（开发中）

### **首页功能按钮**
首页包含以下功能按钮，后续将为每个功能创建对应的子页面：
- 智能跟投
- 指标金股
- 活动中心
- 我的自选
- 充值提现
- 逆向返佣
- 我的持仓
- 公司简介
- 在线客服
- APP下载

### **技术栈**
- **前端**: HTML5, CSS3, JavaScript (ES6+)
- **后端**: PHP
- **样式**: CSS变量 + 响应式设计
- **图标**: Font Awesome 6.0
- **架构**: 模块化组件设计

### **特性**
- 📱 移动端优先设计
- 🎨 现代化UI界面
- 🔄 实时数据更新
- 📊 股票数据展示
- 🔐 用户认证系统
- 🧩 模块化架构

## 🚀 快速开始

1. **启动本地服务器**
   ```bash
   # 使用Python
   python -m http.server 8000

   # 或使用Node.js
   npx serve .
   ```

2. **访问应用**
   ```
   http://localhost:8000
   ```

3. **开发模式**
   - 修改 `public/css/` 下的样式文件
   - 修改 `public/js/` 下的脚本文件
   - 在对应模块文件夹下添加新页面

## 📝 开发计划

### **已完成**
- ✅ 基础文件夹结构
- ✅ 首页模块完整功能
- ✅ 底部导航系统
- ✅ 响应式布局
- ✅ 登录页面和注册页面
- ✅ 用户认证系统
- ✅ 登录状态管理
- ✅ 登录跳转逻辑

### **开发中**
- 🔄 首页功能按钮的子页面
- 🔄 行情模块功能
- 🔄 智投模块功能
- 🔄 个人中心功能

### **计划中**
- 📋 股票交易功能
- 📋 数据可视化图表
- 📋 推送通知系统
- 📋 实时数据推送

## 🔐 认证系统说明

### **登录功能**
- **登录入口**:
  - 首页左上角"登录/注册"按钮
  - 首页右上角用户图标
  - 底部导航"我的"按钮（未登录时）
- **登录方式**: 用户名/手机号 + 密码
- **附加功能**: 记住我、忘记密码、第三方登录（开发中）
- **模拟登录**: 任意用户名 + 6位以上密码即可登录

### **注册功能**
- **注册方式**: 手机号 + 验证码 + 密码设置
- **密码强度**: 实时检测密码强度（弱/中/强）
- **验证码**: 模拟发送验证码（60秒倒计时）
- **用户协议**: 必须同意用户协议和隐私政策

### **认证状态管理**
- **登录状态**: 自动检测和维护登录状态
- **用户信息**: 本地存储用户基本信息
- **自动跳转**: 登录后自动返回原页面
- **登出功能**: 支持手动登出和状态清理

## 🤝 贡献指南

1. 在对应模块文件夹下添加新功能
2. 保持代码风格一致
3. 遵循响应式设计原则
4. 添加必要的注释和文档

---

**国信证券移动端应用** - 专业的移动投资服务平台