# 国信证券移动端应用

这是一个基于HTML、CSS、JavaScript和PHP技术栈开发的国信证券移动端应用界面。

## 项目特性

- 📱 响应式移动端设计
- 🎨 现代化UI界面，高度还原原设计
- 📊 实时股票数据展示
- 🔐 用户登录注册系统
- 📰 新闻资讯功能
- 🛠 完整的后端API支持

## 技术栈

- **前端**: HTML5, CSS3, JavaScript (ES6+)
- **后端**: PHP 7.4+
- **数据库**: MySQL (可选)
- **图标**: Font Awesome 6.0
- **样式**: 原生CSS，渐变效果，动画

## 项目结构

```
zhengquan/
├── index.html          # 主页面
├── login.html          # 登录页面
├── styles.css          # 主样式文件
├── script.js           # 前端JavaScript逻辑
├── api.php            # 后端API接口
└── README.md          # 项目说明文档
```

## 功能模块

### 1. 主页面 (index.html)
- 顶部状态栏和导航
- 营销横幅区域
- 股票指数实时显示
- 功能菜单网格
- 热门新闻展示
- 底部导航栏

### 2. 登录页面 (login.html)
- 用户名/密码登录
- 记住登录状态
- 第三方登录选项
- 表单验证
- 响应式设计

### 3. 后端API (api.php)
- 股票数据接口
- 用户认证接口
- 新闻数据接口
- 搜索功能接口
- JWT Token认证

### 4. 前端交互 (script.js)
- 股票数据实时更新
- 用户交互处理
- 导航切换
- Toast提示
- 数字动画效果

## 安装和运行

### 环境要求
- PHP 7.4 或更高版本
- Web服务器 (Apache/Nginx)
- MySQL 5.7+ (可选)

### 快速开始

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd zhengquan
   ```

2. **配置Web服务器**
   - 将项目文件放置在Web服务器根目录
   - 确保PHP已正确配置

3. **数据库配置** (可选)
   ```php
   // 在 api.php 中修改数据库连接信息
   $host = 'localhost';
   $dbname = 'guosen_securities';
   $username = 'your_username';
   $password = 'your_password';
   ```

4. **启动服务**
   ```bash
   # 使用PHP内置服务器 (开发环境)
   php -S localhost:8000
   
   # 或者配置Apache/Nginx虚拟主机
   ```

5. **访问应用**
   - 打开浏览器访问: `http://localhost:8000`
   - 登录测试账户: 用户名 `demo`, 密码 `123456`

## API接口文档

### 股票数据
```
GET /api.php?path=stock-data
返回: 股票指数实时数据
```

### 用户登录
```
POST /api.php?path=login
参数: {"username": "demo", "password": "123456"}
返回: 用户信息和Token
```

### 新闻列表
```
GET /api.php?path=news&page=1&limit=10
返回: 新闻列表数据
```

### 股票搜索
```
GET /api.php?path=search&keyword=平安
返回: 搜索结果
```

## 界面特性

### 设计亮点
- 🎨 橙色渐变营销横幅
- 📊 股票指数卡片式布局
- 🔲 功能菜单网格设计
- 📱 移动端优化的交互体验
- 🌈 现代化的色彩搭配

### 响应式设计
- 适配不同屏幕尺寸
- 触摸友好的交互元素
- 流畅的动画效果
- 优化的加载性能

## 开发说明

### 自定义配置
1. **修改Logo和品牌信息**
   - 替换 `logo.png` 文件
   - 修改 `index.html` 中的公司名称

2. **调整颜色主题**
   - 编辑 `styles.css` 中的CSS变量
   - 修改渐变色配置

3. **添加新功能**
   - 在 `script.js` 中添加前端逻辑
   - 在 `api.php` 中添加后端接口

### 部署建议
- 使用HTTPS协议保证安全性
- 配置适当的缓存策略
- 压缩CSS和JavaScript文件
- 优化图片资源

## 浏览器兼容性

- ✅ Chrome 60+
- ✅ Firefox 55+
- ✅ Safari 12+
- ✅ Edge 79+
- ✅ 移动端浏览器

## 许可证

本项目仅供学习和演示使用。

## 联系方式

如有问题或建议，请联系开发团队。

---

**注意**: 这是一个演示项目，实际的证券交易应用需要更严格的安全措施和监管合规性。
