/**
 * 国信证券移动端应用 - 常量定义
 * 包含API端点、配置项、枚举值等
 */

// API 配置
const API_CONFIG = {
    BASE_URL: './backend/api',
    ENDPOINTS: {
        STOCK_DATA: '/stock-data.php',
        NEWS: '/news.php',
        LOGIN: '/auth.php?action=login',
        REGISTER: '/auth.php?action=register',
        USER_PROFILE: '/user.php?action=profile',
        SEARCH: '/search.php',
        MARKET_DATA: '/market.php',
        PORTFOLIO: '/portfolio.php',
        TRANSACTIONS: '/transactions.php'
    },
    TIMEOUT: 10000,
    RETRY_ATTEMPTS: 3
};

// 应用配置
const APP_CONFIG = {
    NAME: '国信证券',
    VERSION: '1.0.0',
    DEBUG: true,
    AUTO_REFRESH_INTERVAL: 30000, // 30秒
    TOAST_DURATION: 3000,
    ANIMATION_DURATION: 300
};

// 股票市场配置
const MARKET_CONFIG = {
    INDICES: {
        SHANGHAI: {
            code: '000001',
            name: '上证指数',
            market: 'SH'
        },
        SHENZHEN: {
            code: '399001',
            name: '深证成指',
            market: 'SZ'
        },
        CHINEXT: {
            code: '399006',
            name: '创业板指',
            market: 'SZ'
        }
    },
    TRADING_HOURS: {
        MORNING_START: '09:30',
        MORNING_END: '11:30',
        AFTERNOON_START: '13:00',
        AFTERNOON_END: '15:00'
    },
    REFRESH_INTERVALS: {
        TRADING: 3000,    // 交易时间3秒
        NON_TRADING: 30000 // 非交易时间30秒
    }
};

// 功能菜单配置
const MENU_CONFIG = {
    ITEMS: [
        {
            id: 'smart-investment',
            name: '智能跟投',
            icon: 'fas fa-shopping-cart',
            color: 'yellow',
            url: './src/pages/smart-investment.html',
            requireAuth: true
        },
        {
            id: 'golden-stocks',
            name: '指标金股',
            icon: 'fas fa-chart-line',
            color: 'red',
            url: './src/pages/golden-stocks.html',
            requireAuth: false
        },
        {
            id: 'activity-center',
            name: '活动中心',
            icon: 'fas fa-list',
            color: 'gray',
            url: './src/pages/activity-center.html',
            requireAuth: false
        },
        {
            id: 'my-stocks',
            name: '我的自选',
            icon: 'fas fa-heart',
            color: 'pink',
            url: './src/pages/my-stocks.html',
            requireAuth: true
        },
        {
            id: 'deposit-withdraw',
            name: '充值提现',
            icon: 'fas fa-paper-plane',
            color: 'blue',
            url: './src/pages/deposit-withdraw.html',
            requireAuth: true
        },
        {
            id: 'reverse-commission',
            name: '逆向返佣',
            icon: 'fas fa-chart-bar',
            color: 'red-chart',
            url: './src/pages/reverse-commission.html',
            requireAuth: true
        },
        {
            id: 'my-positions',
            name: '我的持仓',
            icon: 'fas fa-file-alt',
            color: 'green',
            url: './src/pages/my-positions.html',
            requireAuth: true
        },
        {
            id: 'company-info',
            name: '公司简介',
            icon: 'fas fa-dollar-sign',
            color: 'pink-circle',
            url: './src/pages/company-info.html',
            requireAuth: false
        },
        {
            id: 'customer-service',
            name: '在线客服',
            icon: 'fas fa-gem',
            color: 'red-diamond',
            url: './src/pages/customer-service.html',
            requireAuth: false
        },
        {
            id: 'app-download',
            name: 'app下载',
            icon: 'fas fa-download',
            color: 'green-download',
            url: './src/pages/app-download.html',
            requireAuth: false
        }
    ]
};

// 导航配置
const NAVIGATION_CONFIG = {
    PAGES: [
        {
            id: 'home',
            name: '首页',
            icon: 'fas fa-home',
            url: './index.html',
            component: 'HomePage'
        },
        {
            id: 'market',
            name: '行情',
            icon: 'fas fa-chart-line',
            url: './src/pages/market.html',
            component: 'MarketPage'
        },
        {
            id: 'investment',
            name: '智投',
            icon: 'fas fa-envelope',
            url: './src/pages/investment.html',
            component: 'InvestmentPage'
        },
        {
            id: 'profile',
            name: '我的',
            icon: 'fas fa-user',
            url: './src/pages/profile.html',
            component: 'ProfilePage'
        }
    ]
};

// 用户状态
const USER_STATUS = {
    GUEST: 'guest',
    LOGGED_IN: 'logged_in',
    VIP: 'vip',
    PREMIUM: 'premium'
};

// 交易状态
const TRADE_STATUS = {
    PENDING: 'pending',
    EXECUTED: 'executed',
    CANCELLED: 'cancelled',
    FAILED: 'failed'
};

// 股票变化类型
const PRICE_CHANGE_TYPE = {
    UP: 'up',
    DOWN: 'down',
    FLAT: 'flat'
};

// 消息类型
const MESSAGE_TYPE = {
    SUCCESS: 'success',
    ERROR: 'error',
    WARNING: 'warning',
    INFO: 'info'
};

// 本地存储键名
const STORAGE_KEYS = {
    USER_TOKEN: 'guosen_user_token',
    USER_INFO: 'guosen_user_info',
    REMEMBERED_USER: 'guosen_remembered_user',
    SETTINGS: 'guosen_settings',
    CACHE_PREFIX: 'guosen_cache_',
    LAST_VISIT: 'guosen_last_visit'
};

// 错误代码
const ERROR_CODES = {
    NETWORK_ERROR: 'NETWORK_ERROR',
    AUTH_FAILED: 'AUTH_FAILED',
    PERMISSION_DENIED: 'PERMISSION_DENIED',
    DATA_NOT_FOUND: 'DATA_NOT_FOUND',
    SERVER_ERROR: 'SERVER_ERROR',
    VALIDATION_ERROR: 'VALIDATION_ERROR'
};

// 正则表达式
const REGEX_PATTERNS = {
    PHONE: /^1[3-9]\d{9}$/,
    EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    STOCK_CODE: /^[0-9]{6}$/,
    PASSWORD: /^(?=.*[a-zA-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{6,}$/
};

// 格式化配置
const FORMAT_CONFIG = {
    CURRENCY: {
        LOCALE: 'zh-CN',
        CURRENCY: 'CNY',
        MINIMUM_FRACTION_DIGITS: 2,
        MAXIMUM_FRACTION_DIGITS: 2
    },
    NUMBER: {
        LOCALE: 'zh-CN',
        MINIMUM_FRACTION_DIGITS: 0,
        MAXIMUM_FRACTION_DIGITS: 2
    },
    PERCENT: {
        LOCALE: 'zh-CN',
        STYLE: 'percent',
        MINIMUM_FRACTION_DIGITS: 2,
        MAXIMUM_FRACTION_DIGITS: 2
    }
};

// 导出所有常量
if (typeof module !== 'undefined' && module.exports) {
    // Node.js 环境
    module.exports = {
        API_CONFIG,
        APP_CONFIG,
        MARKET_CONFIG,
        MENU_CONFIG,
        NAVIGATION_CONFIG,
        USER_STATUS,
        TRADE_STATUS,
        PRICE_CHANGE_TYPE,
        MESSAGE_TYPE,
        STORAGE_KEYS,
        ERROR_CODES,
        REGEX_PATTERNS,
        FORMAT_CONFIG
    };
} else {
    // 浏览器环境
    window.Constants = {
        API_CONFIG,
        APP_CONFIG,
        MARKET_CONFIG,
        MENU_CONFIG,
        NAVIGATION_CONFIG,
        USER_STATUS,
        TRADE_STATUS,
        PRICE_CHANGE_TYPE,
        MESSAGE_TYPE,
        STORAGE_KEYS,
        ERROR_CODES,
        REGEX_PATTERNS,
        FORMAT_CONFIG
    };
}
