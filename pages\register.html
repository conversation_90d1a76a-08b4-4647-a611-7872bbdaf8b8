<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>注册 - 国信证券</title>
    <link rel="stylesheet" href="../public/css/main.css">
    <link rel="stylesheet" href="../public/css/login.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="login-container">
        <button class="back-btn" onclick="history.back()">
            <i class="fas fa-arrow-left"></i>
        </button>

        <div class="login-box">
            <div class="login-header">
                <div class="login-logo">
                    <i class="fas fa-user-plus"></i>
                </div>
                <h1 class="login-title">欢迎加入</h1>
                <p class="login-subtitle">创建您的国信证券账户</p>
            </div>

            <form id="registerForm">
                <div class="form-group">
                    <label class="form-label">手机号</label>
                    <input type="tel" class="form-input" id="phone" placeholder="请输入手机号" autocomplete="tel">
                    <div class="error-message" id="phoneError">请输入有效的手机号</div>
                </div>

                <div class="form-group">
                    <label class="form-label">验证码</label>
                    <div class="verification-wrapper">
                        <input type="text" class="form-input verification-input" id="verificationCode" placeholder="请输入验证码" maxlength="6">
                        <button type="button" class="send-code-btn" id="sendCodeBtn">
                            <span class="btn-text">发送验证码</span>
                            <span class="countdown hidden">60s</span>
                        </button>
                    </div>
                    <div class="error-message" id="codeError">请输入6位验证码</div>
                </div>

                <div class="form-group">
                    <label class="form-label">设置密码</label>
                    <div class="password-input-wrapper">
                        <input type="password" class="form-input" id="password" placeholder="请设置6-20位密码" autocomplete="new-password">
                        <button type="button" class="password-toggle" id="passwordToggle">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                    <div class="password-strength">
                        <div class="strength-bar">
                            <div class="strength-fill"></div>
                        </div>
                        <span class="strength-text">密码强度：弱</span>
                    </div>
                    <div class="error-message" id="passwordError">密码长度为6-20位</div>
                </div>

                <div class="form-group">
                    <label class="form-label">确认密码</label>
                    <div class="password-input-wrapper">
                        <input type="password" class="form-input" id="confirmPassword" placeholder="请再次输入密码" autocomplete="new-password">
                        <button type="button" class="password-toggle" id="confirmPasswordToggle">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                    <div class="error-message" id="confirmPasswordError">两次密码输入不一致</div>
                </div>

                <div class="form-group">
                    <label class="form-label">邀请码（可选）</label>
                    <input type="text" class="form-input" id="inviteCode" placeholder="请输入邀请码">
                </div>

                <div class="agreement-section">
                    <label class="remember-me">
                        <input type="checkbox" id="agreeTerms" required>
                        <span class="checkmark"></span>
                        <span>我已阅读并同意 <a href="#" class="agreement-link">《用户协议》</a> 和 <a href="#" class="agreement-link">《隐私政策》</a></span>
                    </label>
                </div>

                <button type="submit" class="login-btn" id="registerBtn">
                    <span class="btn-text">立即注册</span>
                    <div class="btn-loading hidden">
                        <i class="fas fa-spinner fa-spin"></i>
                        <span>注册中...</span>
                    </div>
                </button>
            </form>

            <div class="register-link">
                已有账户？<a href="login.html">立即登录</a>
            </div>
        </div>
    </div>

    <!-- Toast 容器 -->
    <div id="toastContainer"></div>

    <!-- 加载脚本 -->
    <script src="../public/js/utils/constants.js"></script>
    <script src="../public/js/utils/helpers.js"></script>
    <script src="../public/js/register.js"></script>
</body>
</html>
