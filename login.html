<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录 - 国信证券</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .login-container {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }

        .login-box {
            background: white;
            border-radius: 16px;
            padding: 40px 30px;
            width: 100%;
            max-width: 400px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }

        .login-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .login-logo {
            width: 60px;
            height: 60px;
            background: linear-gradient(45deg, #1e88e5, #e53935);
            border-radius: 12px;
            margin: 0 auto 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
        }

        .login-title {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            margin-bottom: 8px;
        }

        .login-subtitle {
            color: #666;
            font-size: 14px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-size: 14px;
            font-weight: 500;
        }

        .form-input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        .form-input:focus {
            outline: none;
            border-color: #1976d2;
        }

        .form-input.error {
            border-color: #f44336;
        }

        .error-message {
            color: #f44336;
            font-size: 12px;
            margin-top: 4px;
            display: none;
        }

        .login-btn {
            width: 100%;
            padding: 14px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: transform 0.2s;
            margin-bottom: 20px;
        }

        .login-btn:hover {
            transform: translateY(-2px);
        }

        .login-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .login-options {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
        }

        .remember-me {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            color: #666;
        }

        .forgot-password {
            color: #1976d2;
            text-decoration: none;
            font-size: 14px;
        }

        .divider {
            text-align: center;
            margin: 30px 0;
            position: relative;
            color: #999;
            font-size: 14px;
        }

        .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: #e1e5e9;
            z-index: 1;
        }

        .divider span {
            background: white;
            padding: 0 16px;
            position: relative;
            z-index: 2;
        }

        .social-login {
            display: flex;
            gap: 12px;
            margin-bottom: 30px;
        }

        .social-btn {
            flex: 1;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            background: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            font-size: 14px;
            color: #666;
            transition: border-color 0.3s;
        }

        .social-btn:hover {
            border-color: #1976d2;
        }

        .register-link {
            text-align: center;
            color: #666;
            font-size: 14px;
        }

        .register-link a {
            color: #1976d2;
            text-decoration: none;
        }

        .back-btn {
            position: absolute;
            top: 40px;
            left: 20px;
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            padding: 12px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 18px;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <button class="back-btn" onclick="history.back()">
            <i class="fas fa-arrow-left"></i>
        </button>

        <div class="login-box">
            <div class="login-header">
                <div class="login-logo">
                    <i class="fas fa-chart-line"></i>
                </div>
                <h1 class="login-title">欢迎回来</h1>
                <p class="login-subtitle">登录您的国信证券账户</p>
            </div>

            <form id="loginForm">
                <div class="form-group">
                    <label class="form-label">用户名/手机号</label>
                    <input type="text" class="form-input" id="username" placeholder="请输入用户名或手机号">
                    <div class="error-message" id="usernameError">请输入有效的用户名或手机号</div>
                </div>

                <div class="form-group">
                    <label class="form-label">密码</label>
                    <input type="password" class="form-input" id="password" placeholder="请输入密码">
                    <div class="error-message" id="passwordError">密码不能为空</div>
                </div>

                <div class="login-options">
                    <label class="remember-me">
                        <input type="checkbox" id="rememberMe">
                        <span>记住我</span>
                    </label>
                    <a href="#" class="forgot-password">忘记密码？</a>
                </div>

                <button type="submit" class="login-btn" id="loginBtn">
                    登录
                </button>
            </form>

            <div class="divider">
                <span>或</span>
            </div>

            <div class="social-login">
                <button class="social-btn" onclick="loginWithWeChat()">
                    <i class="fab fa-weixin" style="color: #1aad19;"></i>
                    微信
                </button>
                <button class="social-btn" onclick="loginWithPhone()">
                    <i class="fas fa-mobile-alt" style="color: #1976d2;"></i>
                    手机验证码
                </button>
            </div>

            <div class="register-link">
                还没有账户？<a href="register.html">立即注册</a>
            </div>
        </div>
    </div>

    <script>
        class LoginPage {
            constructor() {
                this.form = document.getElementById('loginForm');
                this.usernameInput = document.getElementById('username');
                this.passwordInput = document.getElementById('password');
                this.loginBtn = document.getElementById('loginBtn');
                this.rememberMe = document.getElementById('rememberMe');
                
                this.bindEvents();
                this.loadRememberedUser();
            }

            bindEvents() {
                this.form.addEventListener('submit', (e) => {
                    e.preventDefault();
                    this.handleLogin();
                });

                // 实时验证
                this.usernameInput.addEventListener('blur', () => {
                    this.validateUsername();
                });

                this.passwordInput.addEventListener('blur', () => {
                    this.validatePassword();
                });

                // 回车登录
                this.passwordInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        this.handleLogin();
                    }
                });
            }

            validateUsername() {
                const username = this.usernameInput.value.trim();
                const isValid = username.length >= 3;
                
                this.toggleError('username', !isValid);
                return isValid;
            }

            validatePassword() {
                const password = this.passwordInput.value;
                const isValid = password.length >= 6;
                
                this.toggleError('password', !isValid);
                return isValid;
            }

            toggleError(field, hasError) {
                const input = document.getElementById(field);
                const error = document.getElementById(field + 'Error');
                
                if (hasError) {
                    input.classList.add('error');
                    error.style.display = 'block';
                } else {
                    input.classList.remove('error');
                    error.style.display = 'none';
                }
            }

            async handleLogin() {
                if (!this.validateUsername() || !this.validatePassword()) {
                    return;
                }

                const username = this.usernameInput.value.trim();
                const password = this.passwordInput.value;

                this.setLoading(true);

                try {
                    const response = await fetch('api.php?path=login', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            username: username,
                            password: password
                        })
                    });

                    const result = await response.json();

                    if (result.success) {
                        // 保存登录状态
                        localStorage.setItem('token', result.data.token);
                        localStorage.setItem('user', JSON.stringify(result.data.user));
                        
                        // 记住用户
                        if (this.rememberMe.checked) {
                            localStorage.setItem('rememberedUser', username);
                        } else {
                            localStorage.removeItem('rememberedUser');
                        }

                        this.showToast('登录成功！', 'success');
                        
                        // 跳转到首页
                        setTimeout(() => {
                            window.location.href = 'index.html';
                        }, 1000);
                    } else {
                        this.showToast(result.error || '登录失败', 'error');
                    }
                } catch (error) {
                    console.error('登录错误:', error);
                    this.showToast('网络错误，请稍后重试', 'error');
                } finally {
                    this.setLoading(false);
                }
            }

            setLoading(loading) {
                this.loginBtn.disabled = loading;
                this.loginBtn.textContent = loading ? '登录中...' : '登录';
            }

            loadRememberedUser() {
                const rememberedUser = localStorage.getItem('rememberedUser');
                if (rememberedUser) {
                    this.usernameInput.value = rememberedUser;
                    this.rememberMe.checked = true;
                }
            }

            showToast(message, type = 'info') {
                const toast = document.createElement('div');
                toast.className = `toast toast-${type}`;
                toast.textContent = message;
                toast.style.cssText = `
                    position: fixed;
                    top: 20px;
                    left: 50%;
                    transform: translateX(-50%);
                    background-color: ${type === 'success' ? '#4caf50' : type === 'error' ? '#f44336' : '#2196f3'};
                    color: white;
                    padding: 12px 24px;
                    border-radius: 8px;
                    z-index: 1000;
                    font-size: 14px;
                    animation: slideDown 0.3s ease;
                `;
                
                document.body.appendChild(toast);
                
                setTimeout(() => {
                    toast.style.animation = 'slideUp 0.3s ease';
                    setTimeout(() => {
                        document.body.removeChild(toast);
                    }, 300);
                }, 2000);
            }
        }

        // 第三方登录
        function loginWithWeChat() {
            alert('微信登录功能开发中...');
        }

        function loginWithPhone() {
            alert('手机验证码登录功能开发中...');
        }

        // 添加动画样式
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideDown {
                from { transform: translateX(-50%) translateY(-100%); opacity: 0; }
                to { transform: translateX(-50%) translateY(0); opacity: 1; }
            }
            @keyframes slideUp {
                from { transform: translateX(-50%) translateY(0); opacity: 1; }
                to { transform: translateX(-50%) translateY(-100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            new LoginPage();
        });
    </script>
</body>
</html>
