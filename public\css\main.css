/*
 * 国信证券移动端应用 - 主样式文件
 * 包含基础样式、布局和通用组件
 */

/* ===== 基础样式 ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* 主题色彩 */
    --primary-color: #1976d2;
    --primary-dark: #1565c0;
    --secondary-color: #ff6b35;
    --accent-color: #ff8f00;
    --success-color: #4caf50;
    --error-color: #f44336;
    --warning-color: #ff9800;
    --info-color: #2196f3;

    /* 中性色彩 */
    --text-primary: #333;
    --text-secondary: #666;
    --text-tertiary: #999;
    --text-disabled: #ccc;
    --background-primary: #fff;
    --background-secondary: #f5f5f5;
    --surface-color: #fff;
    --border-color: #eee;

    /* 间距 */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
    --spacing-xl: 32px;

    /* 圆角 */
    --border-radius-sm: 4px;
    --border-radius-md: 8px;
    --border-radius-lg: 12px;
    --border-radius-xl: 16px;

    /* 阴影 */
    --shadow-sm: 0 2px 4px rgba(0,0,0,0.1);
    --shadow-md: 0 2px 8px rgba(0,0,0,0.1);
    --shadow-lg: 0 4px 16px rgba(0,0,0,0.15);

    /* 字体 */
    --font-size-xs: 12px;
    --font-size-sm: 14px;
    --font-size-md: 16px;
    --font-size-lg: 18px;
    --font-size-xl: 20px;
    --font-size-xxl: 24px;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    background-color: var(--background-secondary);
    color: var(--text-primary);
    line-height: 1.4;
    font-size: var(--font-size-md);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    user-select: none;
}

/* ===== 顶部导航 ===== */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg) var(--spacing-md);
    background-color: var(--background-primary);
    border-bottom: 1px solid var(--border-color);
}

.header-left .login-register {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    cursor: pointer;
    transition: color 0.3s ease;
}

.header-left .login-register:hover {
    color: var(--primary-color);
}

.header-center {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.logo {
    width: 32px;
    height: 32px;
    background: linear-gradient(45deg, var(--primary-color), var(--error-color));
    border-radius: var(--border-radius-sm);
    object-fit: cover;
}

.company-name {
    font-size: var(--font-size-lg);
    font-weight: bold;
    color: var(--text-primary);
}

.header-right {
    display: flex;
    gap: var(--spacing-md);
}

.header-right i {
    font-size: var(--font-size-xl);
    color: var(--text-secondary);
    cursor: pointer;
    transition: color 0.3s ease;
}

.header-right i:hover {
    color: var(--primary-color);
}

.login-register {
    cursor: pointer;
    transition: color 0.3s ease;
}

.login-register.logged-in {
    color: var(--primary-color);
    font-weight: 500;
}

.header-right i {
    cursor: pointer;
}

.header-right i.logged-in {
    color: var(--primary-color);
}

/* ===== 主要内容 ===== */
.main-content {
    padding-bottom: 80px;
    min-height: calc(100vh - 120px);
}

/* ===== 营销横幅 ===== */
.banner {
    background: linear-gradient(135deg, var(--secondary-color), var(--accent-color));
    margin: var(--spacing-md);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    position: relative;
    box-shadow: var(--shadow-md);
}

.banner-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    color: white;
    gap: 0;
}

.banner-text {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
    flex: 1;
    margin-left: 40px;
}

.banner-text h2 {
    font-size: var(--font-size-xxl);
    font-weight: bold;
    margin-bottom: var(--spacing-xs);
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
    white-space: nowrap;
}

.banner-text p {
    font-size: var(--font-size-sm);
    margin-bottom: var(--spacing-xs);
    opacity: 0.9;
    white-space: nowrap;
}

.open-account-btn {
    background-color: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: 20px;
    font-size: var(--font-size-sm);
    margin-top: var(--spacing-sm);
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    width: fit-content;
}

.open-account-btn:hover {
    background-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

.banner-image {
    position: relative;
    width: 180px;
    height: 160px;
    flex-shrink: 0;
    margin-right: 40px;
}

.phone-mockup {
    width: 130px;
    height: 160px;
    background: linear-gradient(45deg, rgba(255,255,255,0.3), rgba(255,255,255,0.1));
    border-radius: var(--border-radius-lg);
    position: absolute;
    right: 0;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.2);
}

.coins {
    position: absolute;
    top: 10px;
    right: 35px;
    width: 60px;
    height: 30px;
    background: radial-gradient(circle, #ffd700, #ffb300);
    border-radius: 50%;
    opacity: 0.9;
    animation: bounce 4s linear infinite;
    z-index: 10;
}

@keyframes bounce {
    0% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(115px);
    }
    100% {
        transform: translateY(0px);
    }
}

/* ===== 响应式设计 ===== */
@media (max-width: 768px) {
    body {
        font-size: var(--font-size-sm);
    }

    .main-content {
        padding-bottom: 70px;
    }
}

@media (max-width: 480px) {
    .banner {
        margin: var(--spacing-sm);
    }

    .banner-content {
        padding: var(--spacing-sm) var(--spacing-xs);
        justify-content: space-between;
        align-items: center;
        gap: 0;
    }

    .banner-text {
        margin-left: 30px;
    }

    .banner-text h2 {
        font-size: var(--font-size-xxl);
    }

    .banner-text p {
        font-size: var(--font-size-sm);
    }

    .header {
        padding: var(--spacing-sm) var(--spacing-md);
    }

    .company-name {
        font-size: var(--font-size-md);
    }

    .banner-image {
        width: 140px;
        height: 130px;
        margin-right: 25px;
    }

    .phone-mockup {
        width: 100px;
        height: 130px;
        position: absolute;
        right: 0;
    }

    .coins {
        width: 40px;
        height: 20px;
        right: 20px;
        top: 8px;
    }

    @keyframes bounce {
        0% {
            transform: translateY(0px);
        }
        50% {
            transform: translateY(60px);
        }
        100% {
            transform: translateY(0px);
        }
    }
}

@media (max-width: 360px) {
    .header {
        padding: var(--spacing-xs) var(--spacing-sm);
    }

    .banner {
        margin: var(--spacing-xs);
    }

    .banner-content {
        padding: var(--spacing-xs);
        justify-content: space-between;
        align-items: center;
        gap: 0;
    }

    .banner-text {
        margin-left: 20px;
    }

    .banner-text h2 {
        font-size: var(--font-size-xxl);
    }

    .banner-text p {
        font-size: var(--font-size-sm);
    }

    .banner-image {
        width: 120px;
        height: 110px;
        margin-right: 20px;
    }

    .phone-mockup {
        width: 85px;
        height: 110px;
        position: absolute;
        right: 0;
    }

    .coins {
        width: 35px;
        height: 18px;
        right: 15px;
        top: 6px;
    }

    @keyframes bounce {
        0% {
            transform: translateY(0px);
        }
        50% {
            transform: translateY(45px);
        }
        100% {
            transform: translateY(0px);
        }
    }

    .company-name {
        font-size: var(--font-size-sm);
    }

    .logo {
        width: 24px;
        height: 24px;
    }
}

/* ===== 页面头部 ===== */
.page-header {
    padding: var(--spacing-lg) var(--spacing-md);
    background-color: var(--background-primary);
    border-bottom: 1px solid var(--border-color);
    text-align: center;
}

.page-header h1 {
    font-size: var(--font-size-xxl);
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.page-header p {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

/* ===== 开发中提示 ===== */
.coming-soon {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-xl);
    text-align: center;
    min-height: 300px;
}

.coming-soon i {
    font-size: 4rem;
    color: var(--text-tertiary);
    margin-bottom: var(--spacing-lg);
}

.coming-soon h2 {
    font-size: var(--font-size-xl);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-sm);
}

.coming-soon p {
    font-size: var(--font-size-md);
    color: var(--text-tertiary);
}

/* ===== 用户菜单 ===== */
.user-menu {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    display: flex;
    justify-content: center;
    align-items: flex-start;
    padding-top: 80px;
    animation: fadeIn 0.3s ease;
}

.user-menu-content {
    background: white;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    min-width: 280px;
    max-width: 90vw;
    animation: slideDown 0.3s ease;
}

.user-info {
    padding: var(--spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    border-bottom: 1px solid var(--border-color);
}

.user-avatar {
    width: 50px;
    height: 50px;
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
}

.user-details {
    flex: 1;
}

.user-name {
    font-size: var(--font-size-lg);
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.user-phone {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.menu-divider {
    height: 1px;
    background: var(--border-color);
}

.menu-items {
    padding: var(--spacing-sm) 0;
}

.user-menu .menu-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md) var(--spacing-lg);
    cursor: pointer;
    transition: background-color 0.3s ease;
    font-size: var(--font-size-md);
    color: var(--text-primary);
}

.user-menu .menu-item:hover {
    background-color: var(--background-secondary);
}

.user-menu .menu-item.logout {
    color: var(--error-color);
}

.user-menu .menu-item.logout:hover {
    background-color: rgba(244, 67, 54, 0.1);
}

.user-menu .menu-item i {
    width: 20px;
    text-align: center;
}

/* ===== 工具类 ===== */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-xs { margin-bottom: var(--spacing-xs); }
.mb-sm { margin-bottom: var(--spacing-sm); }
.mb-md { margin-bottom: var(--spacing-md); }
.mb-lg { margin-bottom: var(--spacing-lg); }

.mt-xs { margin-top: var(--spacing-xs); }
.mt-sm { margin-top: var(--spacing-sm); }
.mt-md { margin-top: var(--spacing-md); }
.mt-lg { margin-top: var(--spacing-lg); }

.p-xs { padding: var(--spacing-xs); }
.p-sm { padding: var(--spacing-sm); }
.p-md { padding: var(--spacing-md); }
.p-lg { padding: var(--spacing-lg); }

.hidden { display: none; }
.visible { display: block; }

.loading {
    opacity: 0.6;
    pointer-events: none;
}

/* ===== 动画 ===== */
.fade-in {
    animation: fadeIn 0.3s ease-in;
}

.slide-up {
    animation: slideUp 0.3s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
