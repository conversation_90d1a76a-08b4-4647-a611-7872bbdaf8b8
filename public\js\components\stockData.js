/**
 * 股票数据组件
 * 负责获取、更新和显示股票指数数据
 */

class StockDataComponent {
    constructor() {
        this.data = new Map();
        this.updateInterval = null;
        this.isUpdating = false;
        this.retryCount = 0;
        this.maxRetries = 3;

        this.init();
    }

    /**
     * 初始化组件
     */
    init() {
        this.bindElements();
        this.loadInitialData();
        this.startAutoUpdate();
    }

    /**
     * 绑定DOM元素
     */
    bindElements() {
        this.stockIndicesContainer = document.querySelector('.stock-indices');
        this.indexItems = document.querySelectorAll('.index-item');
    }

    /**
     * 加载初始数据
     */
    async loadInitialData() {
        try {
            await this.fetchStockData();
            this.updateDisplay();
        } catch (error) {
            console.error('Failed to load initial stock data:', error);
            this.showError('加载股票数据失败');
        }
    }

    /**
     * 获取股票数据
     */
    async fetchStockData() {
        if (this.isUpdating) return;

        this.isUpdating = true;

        try {
            // 暂时使用模拟数据，避免API错误
            const mockData = this.generateMockStockData();
            this.processStockData(mockData);
            this.retryCount = 0;

            // 如果需要真实API，取消注释下面的代码
            /*
            const response = await fetch(`${Constants.API_CONFIG.BASE_URL}${Constants.API_CONFIG.ENDPOINTS.STOCK_DATA}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                },
                timeout: Constants.API_CONFIG.TIMEOUT
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result = await response.json();

            if (result.success) {
                this.processStockData(result.data);
                this.retryCount = 0;
            } else {
                throw new Error(result.error || '获取数据失败');
            }
            */
        } catch (error) {
            console.error('Fetch stock data error:', error);

            // 使用模拟数据作为后备
            const mockData = this.generateMockStockData();
            this.processStockData(mockData);
        } finally {
            this.isUpdating = false;
        }
    }

    /**
     * 生成模拟股票数据
     */
    generateMockStockData() {
        const baseData = [
            {
                code: '000001',
                name: '上证指数',
                baseValue: 3348.37,
                baseChange: -31.82,
                basePercent: -0.94
            },
            {
                code: '399001',
                name: '深证成指',
                baseValue: 10132.41,
                baseChange: -87.21,
                basePercent: -0.85
            },
            {
                code: '399006',
                name: '创业板指',
                baseValue: 2021.5,
                baseChange: -24.07,
                basePercent: -1.18
            }
        ];

        return baseData.map(stock => {
            // 添加小幅随机波动
            const volatility = 0.005; // 0.5%的波动
            const randomFactor = (Math.random() - 0.5) * 2 * volatility;

            const currentValue = stock.baseValue * (1 + randomFactor);
            const change = currentValue - stock.baseValue;
            const percent = (change / stock.baseValue) * 100;

            return {
                code: stock.code,
                name: stock.name,
                value: parseFloat(currentValue.toFixed(2)),
                change: parseFloat(change.toFixed(2)),
                percent: parseFloat(percent.toFixed(2)),
                volume: Math.floor(Math.random() * 9000000) + 1000000,
                timestamp: Date.now()
            };
        });
    }

    /**
     * 处理股票数据
     */
    processStockData(stockData) {
        if (!Array.isArray(stockData)) {
            console.error('Invalid stock data format');
            return;
        }

        stockData.forEach(item => {
            const previousData = this.data.get(item.code);

            // 计算价格变化趋势
            if (previousData) {
                item.trend = this.calculateTrend(previousData.value, item.value);
            } else {
                item.trend = Constants.PRICE_CHANGE_TYPE.FLAT;
            }

            // 存储数据
            this.data.set(item.code, {
                ...item,
                lastUpdate: Date.now()
            });
        });
    }

    /**
     * 计算价格趋势
     */
    calculateTrend(oldValue, newValue) {
        if (newValue > oldValue) {
            return Constants.PRICE_CHANGE_TYPE.UP;
        } else if (newValue < oldValue) {
            return Constants.PRICE_CHANGE_TYPE.DOWN;
        } else {
            return Constants.PRICE_CHANGE_TYPE.FLAT;
        }
    }

    /**
     * 更新显示
     */
    updateDisplay() {
        this.indexItems.forEach(item => {
            const indexName = item.querySelector('.index-name').textContent;
            const stockData = this.findStockDataByName(indexName);

            if (stockData) {
                this.updateIndexItem(item, stockData);
            }
        });
    }

    /**
     * 根据名称查找股票数据
     */
    findStockDataByName(name) {
        for (const [code, data] of this.data) {
            if (data.name === name) {
                return data;
            }
        }
        return null;
    }

    /**
     * 更新单个指数项
     */
    updateIndexItem(element, data) {
        const valueElement = element.querySelector('.index-value');
        const changeElement = element.querySelector('.index-change');

        // 添加更新动画
        element.classList.add('updating');

        // 更新数值
        this.animateNumber(valueElement, data.value, {
            duration: 1000,
            decimals: 2
        });

        // 更新涨跌信息
        const changeSpans = changeElement.querySelectorAll('span');
        if (changeSpans.length >= 2) {
            changeSpans[0].textContent = data.change >= 0 ? `+${data.change.toFixed(2)}` : data.change.toFixed(2);
            changeSpans[1].textContent = Utils.formatPercent(data.percent, true);
        }

        // 设置颜色
        const changeClass = data.change >= 0 ? 'positive' : 'negative';
        changeElement.className = `index-change ${changeClass}`;

        // 添加趋势指示
        this.addTrendIndicator(element, data.trend);

        // 移除更新动画
        setTimeout(() => {
            element.classList.remove('updating');
        }, 1000);
    }

    /**
     * 数字动画
     */
    animateNumber(element, targetValue, options = {}) {
        const {
            duration = 1000,
            decimals = 2,
            easing = 'easeOutQuart'
        } = options;

        const startValue = parseFloat(element.textContent) || 0;
        const startTime = Date.now();

        const animate = () => {
            const elapsed = Date.now() - startTime;
            const progress = Math.min(elapsed / duration, 1);

            // 缓动函数
            const easedProgress = this.easeOutQuart(progress);

            const currentValue = startValue + (targetValue - startValue) * easedProgress;
            element.textContent = currentValue.toFixed(decimals);

            if (progress < 1) {
                requestAnimationFrame(animate);
            }
        };

        animate();
    }

    /**
     * 缓动函数
     */
    easeOutQuart(t) {
        return 1 - (--t) * t * t * t;
    }

    /**
     * 添加趋势指示器
     */
    addTrendIndicator(element, trend) {
        // 移除现有的趋势指示器
        const existingIndicator = element.querySelector('.trend-indicator');
        if (existingIndicator) {
            existingIndicator.remove();
        }

        if (trend === Constants.PRICE_CHANGE_TYPE.FLAT) {
            return;
        }

        const indicator = document.createElement('div');
        indicator.className = `trend-indicator ${trend}`;
        indicator.innerHTML = trend === Constants.PRICE_CHANGE_TYPE.UP ? '↗' : '↘';

        element.appendChild(indicator);

        // 自动移除指示器
        setTimeout(() => {
            indicator.remove();
        }, 3000);
    }

    /**
     * 开始自动更新
     */
    startAutoUpdate() {
        // 清除现有的定时器
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
        }

        // 根据交易时间设置不同的更新频率
        const interval = this.isMarketOpen() ?
            Constants.MARKET_CONFIG.REFRESH_INTERVALS.TRADING :
            Constants.MARKET_CONFIG.REFRESH_INTERVALS.NON_TRADING;

        this.updateInterval = setInterval(() => {
            this.fetchStockData();
        }, interval);
    }

    /**
     * 停止自动更新
     */
    stopAutoUpdate() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
            this.updateInterval = null;
        }
    }

    /**
     * 检查市场是否开放
     */
    isMarketOpen() {
        const now = new Date();
        const currentTime = now.getHours() * 100 + now.getMinutes();
        const dayOfWeek = now.getDay();

        // 周末不开市
        if (dayOfWeek === 0 || dayOfWeek === 6) {
            return false;
        }

        // 检查交易时间
        const morningStart = 930; // 09:30
        const morningEnd = 1130;  // 11:30
        const afternoonStart = 1300; // 13:00
        const afternoonEnd = 1500;   // 15:00

        return (currentTime >= morningStart && currentTime <= morningEnd) ||
               (currentTime >= afternoonStart && currentTime <= afternoonEnd);
    }

    /**
     * 显示错误信息
     */
    showError(message) {
        if (window.Toast) {
            Toast.show(message, Constants.MESSAGE_TYPE.ERROR);
        } else {
            console.error(message);
        }
    }

    /**
     * 手动刷新数据
     */
    async refresh(showToast = true) {
        if (this.isUpdating) {
            return;
        }

        try {
            await this.fetchStockData();
            this.updateDisplay();

            // 只有手动刷新时才显示提示
            if (showToast && window.Toast) {
                Toast.show('数据已更新', Constants.MESSAGE_TYPE.SUCCESS);
            }
        } catch (error) {
            this.showError('刷新失败，请稍后重试');
        }
    }

    /**
     * 获取指定股票的数据
     */
    getStockData(code) {
        return this.data.get(code);
    }

    /**
     * 获取所有股票数据
     */
    getAllStockData() {
        return Array.from(this.data.values());
    }

    /**
     * 销毁组件
     */
    destroy() {
        this.stopAutoUpdate();
        this.data.clear();
    }
}

// 导出组件
if (typeof module !== 'undefined' && module.exports) {
    module.exports = StockDataComponent;
} else {
    window.StockDataComponent = StockDataComponent;
}
