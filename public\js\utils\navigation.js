/**
 * 导航工具类
 * 提供统一的页面跳转功能
 */

class NavigationUtils {
    /**
     * 页面路径配置
     */
    static PAGES = {
        // 主要页面
        HOME: 'home/index.html',
        LOGIN: 'login.html',
        REGISTER: 'register.html',
        
        // 模块页面
        MARKET: 'market/index.html',
        SMART_INVEST: 'smart-invest/index.html',
        PROFILE: 'profile/index.html'
    };

    /**
     * 获取当前页面的基础路径
     */
    static getCurrentBasePath() {
        const currentPath = window.location.pathname;
        
        // 如果在根目录
        if (currentPath === '/' || currentPath.endsWith('/index.html')) {
            return './pages/';
        }
        
        // 如果在pages目录下
        if (currentPath.includes('/pages/')) {
            // 检查是否在子目录中（如 /pages/home/<USER>
            const pathParts = currentPath.split('/pages/')[1].split('/');
            if (pathParts.length > 1) {
                // 在子目录中，需要返回上一级
                return '../';
            } else {
                // 在pages根目录中
                return './';
            }
        }
        
        // 默认情况
        return './pages/';
    }

    /**
     * 跳转到指定页面
     */
    static navigateTo(page, saveReturnUrl = false) {
        try {
            // 保存返回URL
            if (saveReturnUrl) {
                localStorage.setItem('returnUrl', window.location.href);
            }

            // 获取目标路径
            let targetPath;
            
            // 如果是预定义的页面标识符
            if (this.PAGES[page]) {
                targetPath = this.getCurrentBasePath() + this.PAGES[page];
            } 
            // 如果是直接的路径
            else if (typeof page === 'string') {
                targetPath = this.getCurrentBasePath() + page;
            } 
            else {
                throw new Error(`Invalid page identifier: ${page}`);
            }

            // 执行跳转
            window.location.href = targetPath;
            
        } catch (error) {
            console.error('Navigation error:', error);
            // 降级处理：跳转到首页
            this.goHome();
        }
    }

    /**
     * 跳转到首页
     */
    static goHome() {
        const basePath = this.getCurrentBasePath();
        window.location.href = basePath + this.PAGES.HOME;
    }

    /**
     * 跳转到登录页面
     */
    static goLogin() {
        this.navigateTo('LOGIN', true);
    }

    /**
     * 跳转到注册页面
     */
    static goRegister() {
        this.navigateTo('REGISTER', true);
    }

    /**
     * 登录后的跳转处理
     */
    static handleLoginSuccess() {
        const returnUrl = localStorage.getItem('returnUrl');
        
        if (returnUrl) {
            localStorage.removeItem('returnUrl');
            window.location.href = returnUrl;
        } else {
            this.goHome();
        }
    }
}

// 全局导出
window.NavigationUtils = NavigationUtils;

// 简化的全局函数
window.goHome = () => NavigationUtils.goHome();
window.goLogin = () => NavigationUtils.goLogin();
window.goRegister = () => NavigationUtils.goRegister();
