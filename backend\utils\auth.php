<?php
/**
 * 用户认证工具类
 * 处理用户登录、注册、Token验证等
 */

require_once __DIR__ . '/../config/database.php';

class Auth {
    private static $secretKey = 'guosen_securities_secret_key_2024';
    private static $tokenExpiry = 86400; // 24小时
    
    /**
     * 用户登录
     */
    public static function login($username, $password) {
        try {
            $db = Database::getInstance();
            
            // 查询用户
            $user = $db->fetchOne(
                "SELECT * FROM users WHERE username = ? AND status = 'active'",
                [$username]
            );
            
            if (!$user) {
                return ['success' => false, 'message' => '用户不存在或已被禁用'];
            }
            
            // 验证密码
            if (!password_verify($password, $user['password'])) {
                return ['success' => false, 'message' => '密码错误'];
            }
            
            // 生成Token
            $token = self::generateToken($user['id']);
            
            // 保存会话
            $expiresAt = date('Y-m-d H:i:s', time() + self::$tokenExpiry);
            $db->insert('user_sessions', [
                'user_id' => $user['id'],
                'token' => $token,
                'expires_at' => $expiresAt
            ]);
            
            // 更新最后登录时间
            $db->update('users', 
                ['updated_at' => date('Y-m-d H:i:s')],
                'id = ?',
                [$user['id']]
            );
            
            // 返回用户信息（不包含密码）
            unset($user['password']);
            
            return [
                'success' => true,
                'message' => '登录成功',
                'data' => [
                    'token' => $token,
                    'user' => $user,
                    'expires_at' => $expiresAt
                ]
            ];
            
        } catch (Exception $e) {
            error_log("Login error: " . $e->getMessage());
            return ['success' => false, 'message' => '登录失败，请稍后重试'];
        }
    }
    
    /**
     * 用户注册
     */
    public static function register($userData) {
        try {
            $db = Database::getInstance();
            
            // 验证必需字段
            $required = ['username', 'password'];
            foreach ($required as $field) {
                if (empty($userData[$field])) {
                    return ['success' => false, 'message' => "缺少必需字段: {$field}"];
                }
            }
            
            // 检查用户名是否已存在
            $existingUser = $db->fetchOne(
                "SELECT id FROM users WHERE username = ?",
                [$userData['username']]
            );
            
            if ($existingUser) {
                return ['success' => false, 'message' => '用户名已存在'];
            }
            
            // 检查邮箱是否已存在（如果提供）
            if (!empty($userData['email'])) {
                $existingEmail = $db->fetchOne(
                    "SELECT id FROM users WHERE email = ?",
                    [$userData['email']]
                );
                
                if ($existingEmail) {
                    return ['success' => false, 'message' => '邮箱已被使用'];
                }
            }
            
            // 检查手机号是否已存在（如果提供）
            if (!empty($userData['phone'])) {
                $existingPhone = $db->fetchOne(
                    "SELECT id FROM users WHERE phone = ?",
                    [$userData['phone']]
                );
                
                if ($existingPhone) {
                    return ['success' => false, 'message' => '手机号已被使用'];
                }
            }
            
            // 加密密码
            $hashedPassword = password_hash($userData['password'], PASSWORD_DEFAULT);
            
            // 准备插入数据
            $insertData = [
                'username' => $userData['username'],
                'password' => $hashedPassword,
                'email' => $userData['email'] ?? null,
                'phone' => $userData['phone'] ?? null,
                'nickname' => $userData['nickname'] ?? $userData['username'],
                'avatar' => $userData['avatar'] ?? null
            ];
            
            // 插入用户
            $userId = $db->insert('users', $insertData);
            
            return [
                'success' => true,
                'message' => '注册成功',
                'data' => ['user_id' => $userId]
            ];
            
        } catch (Exception $e) {
            error_log("Register error: " . $e->getMessage());
            return ['success' => false, 'message' => '注册失败，请稍后重试'];
        }
    }
    
    /**
     * 验证Token
     */
    public static function validateToken($token) {
        try {
            if (empty($token)) {
                return ['success' => false, 'message' => 'Token不能为空'];
            }
            
            $db = Database::getInstance();
            
            // 查询会话
            $session = $db->fetchOne(
                "SELECT s.*, u.* FROM user_sessions s 
                 JOIN users u ON s.user_id = u.id 
                 WHERE s.token = ? AND s.expires_at > NOW() AND u.status = 'active'",
                [$token]
            );
            
            if (!$session) {
                return ['success' => false, 'message' => 'Token无效或已过期'];
            }
            
            // 移除敏感信息
            unset($session['password']);
            
            return [
                'success' => true,
                'message' => 'Token验证成功',
                'data' => $session
            ];
            
        } catch (Exception $e) {
            error_log("Token validation error: " . $e->getMessage());
            return ['success' => false, 'message' => 'Token验证失败'];
        }
    }
    
    /**
     * 用户登出
     */
    public static function logout($token) {
        try {
            if (empty($token)) {
                return ['success' => false, 'message' => 'Token不能为空'];
            }
            
            $db = Database::getInstance();
            
            // 删除会话
            $affected = $db->delete('user_sessions', 'token = ?', [$token]);
            
            if ($affected > 0) {
                return ['success' => true, 'message' => '登出成功'];
            } else {
                return ['success' => false, 'message' => 'Token不存在'];
            }
            
        } catch (Exception $e) {
            error_log("Logout error: " . $e->getMessage());
            return ['success' => false, 'message' => '登出失败'];
        }
    }
    
    /**
     * 刷新Token
     */
    public static function refreshToken($token) {
        try {
            $validation = self::validateToken($token);
            
            if (!$validation['success']) {
                return $validation;
            }
            
            $db = Database::getInstance();
            $userId = $validation['data']['user_id'];
            
            // 生成新Token
            $newToken = self::generateToken($userId);
            $expiresAt = date('Y-m-d H:i:s', time() + self::$tokenExpiry);
            
            // 更新会话
            $db->update('user_sessions',
                [
                    'token' => $newToken,
                    'expires_at' => $expiresAt
                ],
                'token = ?',
                [$token]
            );
            
            return [
                'success' => true,
                'message' => 'Token刷新成功',
                'data' => [
                    'token' => $newToken,
                    'expires_at' => $expiresAt
                ]
            ];
            
        } catch (Exception $e) {
            error_log("Token refresh error: " . $e->getMessage());
            return ['success' => false, 'message' => 'Token刷新失败'];
        }
    }
    
    /**
     * 获取当前用户信息
     */
    public static function getCurrentUser($token) {
        $validation = self::validateToken($token);
        
        if ($validation['success']) {
            return [
                'success' => true,
                'data' => $validation['data']
            ];
        }
        
        return $validation;
    }
    
    /**
     * 生成Token
     */
    private static function generateToken($userId) {
        $payload = [
            'user_id' => $userId,
            'issued_at' => time(),
            'expires_at' => time() + self::$tokenExpiry,
            'random' => bin2hex(random_bytes(16))
        ];
        
        $header = base64_encode(json_encode(['typ' => 'JWT', 'alg' => 'HS256']));
        $payload = base64_encode(json_encode($payload));
        $signature = hash_hmac('sha256', $header . '.' . $payload, self::$secretKey);
        
        return $header . '.' . $payload . '.' . $signature;
    }
    
    /**
     * 从请求头获取Token
     */
    public static function getTokenFromRequest() {
        $headers = getallheaders();
        
        // 检查Authorization头
        if (isset($headers['Authorization'])) {
            $authHeader = $headers['Authorization'];
            if (preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
                return $matches[1];
            }
        }
        
        // 检查X-Auth-Token头
        if (isset($headers['X-Auth-Token'])) {
            return $headers['X-Auth-Token'];
        }
        
        // 检查GET参数
        if (isset($_GET['token'])) {
            return $_GET['token'];
        }
        
        return null;
    }
    
    /**
     * 中间件：要求用户登录
     */
    public static function requireAuth() {
        $token = self::getTokenFromRequest();
        $validation = self::validateToken($token);
        
        if (!$validation['success']) {
            Response::unauthorized($validation['message']);
        }
        
        return $validation['data'];
    }
    
    /**
     * 清理过期会话
     */
    public static function cleanExpiredSessions() {
        try {
            $db = Database::getInstance();
            $affected = $db->delete('user_sessions', 'expires_at < NOW()');
            
            if ($affected > 0) {
                error_log("Cleaned {$affected} expired sessions");
            }
            
            return $affected;
            
        } catch (Exception $e) {
            error_log("Clean expired sessions error: " . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * 验证密码强度
     */
    public static function validatePasswordStrength($password) {
        $errors = [];
        
        if (strlen($password) < 6) {
            $errors[] = '密码长度至少6位';
        }
        
        if (!preg_match('/[a-zA-Z]/', $password)) {
            $errors[] = '密码必须包含字母';
        }
        
        if (!preg_match('/\d/', $password)) {
            $errors[] = '密码必须包含数字';
        }
        
        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }
    
    /**
     * 生成随机密码
     */
    public static function generateRandomPassword($length = 8) {
        $chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        return substr(str_shuffle($chars), 0, $length);
    }
}

// 定期清理过期会话（可以通过cron job调用）
if (php_sapi_name() === 'cli' && isset($argv[1]) && $argv[1] === 'clean-sessions') {
    Auth::cleanExpiredSessions();
}
?>
