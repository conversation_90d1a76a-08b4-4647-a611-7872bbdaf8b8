* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: #f5f5f5;
    color: #333;
    line-height: 1.4;
}

/* 状态栏 */
.status-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 16px;
    background-color: #fff;
    font-size: 14px;
    color: #333;
    border-bottom: 1px solid #eee;
}

.status-left i {
    margin-right: 8px;
}

.status-right {
    display: flex;
    align-items: center;
    gap: 8px;
}

/* 顶部导航 */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background-color: #fff;
    border-bottom: 1px solid #eee;
}

.header-left .login-register {
    color: #666;
    font-size: 14px;
}

.header-center {
    display: flex;
    align-items: center;
    gap: 8px;
}

.logo {
    width: 32px;
    height: 32px;
    background: linear-gradient(45deg, #1e88e5, #e53935);
    border-radius: 4px;
}

.company-name {
    font-size: 18px;
    font-weight: bold;
    color: #333;
}

.header-right {
    display: flex;
    gap: 16px;
}

.header-right i {
    font-size: 20px;
    color: #666;
    cursor: pointer;
}

/* 主要内容 */
.main-content {
    padding-bottom: 80px;
}

/* 营销横幅 */
.banner {
    background: linear-gradient(135deg, #ff6b35, #ff8f00);
    margin: 16px;
    border-radius: 12px;
    overflow: hidden;
    position: relative;
}

.banner-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24px;
    color: white;
}

.banner-text h2 {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 4px;
}

.banner-text p {
    font-size: 14px;
    margin-bottom: 4px;
    opacity: 0.9;
}

.open-account-btn {
    background-color: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    margin-top: 12px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    backdrop-filter: blur(10px);
}

.banner-image {
    position: relative;
    width: 120px;
    height: 100px;
}

.phone-mockup {
    width: 80px;
    height: 100px;
    background: linear-gradient(45deg, rgba(255,255,255,0.3), rgba(255,255,255,0.1));
    border-radius: 12px;
    position: absolute;
    right: 0;
    backdrop-filter: blur(10px);
}

.coins {
    position: absolute;
    bottom: 0;
    right: 20px;
    width: 40px;
    height: 20px;
    background: radial-gradient(circle, #ffd700, #ffb300);
    border-radius: 50%;
    opacity: 0.8;
}

/* 股票指数 */
.stock-indices {
    display: flex;
    background-color: white;
    margin: 16px;
    border-radius: 12px;
    padding: 16px 0;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.index-item {
    flex: 1;
    text-align: center;
    padding: 0 8px;
}

.index-item:not(:last-child) {
    border-right: 1px solid #eee;
}

.index-name {
    font-size: 12px;
    color: #666;
    margin-bottom: 8px;
}

.index-value {
    font-size: 18px;
    font-weight: bold;
    color: #333;
    margin-bottom: 4px;
}

.index-change {
    font-size: 12px;
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.index-change.negative {
    color: #4caf50;
}

.index-change.positive {
    color: #f44336;
}

/* 功能菜单 */
.function-menu {
    background-color: white;
    margin: 16px;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.menu-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 24px;
}

.menu-row:last-child {
    margin-bottom: 0;
}

.menu-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    flex: 1;
    cursor: pointer;
}

.menu-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    color: white;
}

.menu-icon.yellow { background: linear-gradient(45deg, #ffc107, #ff9800); }
.menu-icon.red { background: linear-gradient(45deg, #f44336, #e91e63); }
.menu-icon.gray { background: linear-gradient(45deg, #9e9e9e, #757575); }
.menu-icon.pink { background: linear-gradient(45deg, #e91e63, #f06292); }
.menu-icon.blue { background: linear-gradient(45deg, #2196f3, #1976d2); }
.menu-icon.red-chart { background: linear-gradient(45deg, #f44336, #d32f2f); }
.menu-icon.green { background: linear-gradient(45deg, #4caf50, #388e3c); }
.menu-icon.pink-circle { background: linear-gradient(45deg, #ff4081, #e91e63); }
.menu-icon.red-diamond { background: linear-gradient(45deg, #f44336, #c62828); }
.menu-icon.green-download { background: linear-gradient(45deg, #4caf50, #2e7d32); }

.menu-item span {
    font-size: 12px;
    color: #666;
    text-align: center;
}

/* 新闻区域 */
.news-section {
    background-color: white;
    margin: 16px;
    border-radius: 12px;
    padding: 16px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.news-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.news-header h3 {
    font-size: 16px;
    font-weight: bold;
    color: #333;
}

.more-link {
    font-size: 14px;
    color: #666;
    cursor: pointer;
}

.news-item {
    display: flex;
    gap: 12px;
    align-items: center;
}

.news-content {
    flex: 1;
}

.news-content h4 {
    font-size: 14px;
    color: #333;
    line-height: 1.4;
}

.news-image {
    width: 80px;
    height: 60px;
    border-radius: 8px;
    overflow: hidden;
    background: linear-gradient(45deg, #f44336, #e53935);
}

.news-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* 底部导航 */
.bottom-nav {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: white;
    display: flex;
    border-top: 1px solid #eee;
    z-index: 100;
}

.nav-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 8px;
    cursor: pointer;
    color: #666;
}

.nav-item.active {
    color: #1976d2;
}

.nav-item i {
    font-size: 20px;
    margin-bottom: 4px;
}

.nav-item span {
    font-size: 12px;
}



/* 响应式设计 */
@media (max-width: 480px) {
    .banner-content {
        padding: 16px;
    }

    .banner-text h2 {
        font-size: 20px;
    }

    .menu-icon {
        width: 40px;
        height: 40px;
        font-size: 18px;
    }

    .menu-item span {
        font-size: 11px;
    }
}
