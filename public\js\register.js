/**
 * 注册页面脚本
 * 处理用户注册相关功能
 */

class RegisterPage {
    constructor() {
        this.form = null;
        this.inputs = {};
        this.isSubmitting = false;
        this.countdownTimer = null;
        this.countdownSeconds = 60;

        this.init();
    }

    /**
     * 初始化页面
     */
    init() {
        this.bindElements();
        this.bindEvents();
        this.initValidation();
    }

    /**
     * 绑定DOM元素
     */
    bindElements() {
        this.form = document.getElementById('registerForm');
        this.inputs = {
            username: document.getElementById('username'),
            password: document.getElementById('password'),
            confirmPassword: document.getElementById('confirmPassword')
        };

        this.buttons = {
            register: document.getElementById('registerBtn'),
            passwordToggle: document.getElementById('passwordToggle'),
            confirmPasswordToggle: document.getElementById('confirmPasswordToggle')
        };
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // 表单提交
        this.form.addEventListener('submit', (e) => this.handleSubmit(e));

        // 密码显示/隐藏
        this.buttons.passwordToggle.addEventListener('click', () => this.togglePassword('password'));
        this.buttons.confirmPasswordToggle.addEventListener('click', () => this.togglePassword('confirmPassword'));

        // 实时验证
        this.inputs.username.addEventListener('input', () => this.validateUsername());
        this.inputs.password.addEventListener('input', () => this.validatePassword());
        this.inputs.confirmPassword.addEventListener('input', () => this.validateConfirmPassword());
    }

    /**
     * 初始化验证
     */
    initValidation() {
        // 失焦验证
        this.inputs.username.addEventListener('blur', () => this.validateUsername());
        this.inputs.password.addEventListener('blur', () => this.validatePassword());
        this.inputs.confirmPassword.addEventListener('blur', () => this.validateConfirmPassword());
    }

    /**
     * 处理表单提交
     */
    async handleSubmit(e) {
        e.preventDefault();

        if (this.isSubmitting) return;

        // 验证所有字段
        const isValid = this.validateAll();
        if (!isValid) {
            this.showToast('请检查输入信息', 'error');
            return;
        }

        this.isSubmitting = true;
        this.setSubmitLoading(true);

        try {
            const formData = this.getFormData();
            const result = await this.submitRegistration(formData);

            if (result.success) {
                this.showToast('注册成功！正在跳转...', 'success');
                setTimeout(() => {
                    window.location.href = 'login.html';
                }, 2000);
            } else {
                this.showToast(result.message || '注册失败，请重试', 'error');
            }
        } catch (error) {
            console.error('Registration error:', error);
            this.showToast('网络错误，请重试', 'error');
        } finally {
            this.isSubmitting = false;
            this.setSubmitLoading(false);
        }
    }



    /**
     * 切换密码显示
     */
    togglePassword(fieldName) {
        const input = this.inputs[fieldName];
        const button = this.buttons[fieldName + 'Toggle'];
        const icon = button.querySelector('i');

        if (input.type === 'password') {
            input.type = 'text';
            icon.className = 'fas fa-eye-slash';
        } else {
            input.type = 'password';
            icon.className = 'fas fa-eye';
        }
    }



    /**
     * 验证所有字段
     */
    validateAll() {
        const validations = [
            this.validateUsername(),
            this.validatePassword(),
            this.validateConfirmPassword()
        ];

        return validations.every(result => result);
    }

    /**
     * 验证用户名
     */
    validateUsername() {
        const username = this.inputs.username.value.trim();

        if (!username) {
            this.showFieldError('username', '请输入账号');
            return false;
        }

        if (username.length < 3) {
            this.showFieldError('username', '账号长度至少3位');
            return false;
        }

        if (username.length > 20) {
            this.showFieldError('username', '账号长度不能超过20位');
            return false;
        }

        this.hideFieldError('username');
        return true;
    }

    /**
     * 验证密码
     */
    validatePassword() {
        const password = this.inputs.password.value;

        if (!password) {
            this.showFieldError('password', '请输入密码');
            return false;
        }

        if (password.length < 6 || password.length > 20) {
            this.showFieldError('password', '密码长度为6-20位');
            return false;
        }

        this.hideFieldError('password');
        return true;
    }

    /**
     * 验证确认密码
     */
    validateConfirmPassword() {
        const password = this.inputs.password.value;
        const confirmPassword = this.inputs.confirmPassword.value;

        if (!confirmPassword) {
            this.showFieldError('confirmPassword', '请确认密码');
            return false;
        }

        if (password !== confirmPassword) {
            this.showFieldError('confirmPassword', '两次密码输入不一致');
            return false;
        }

        this.hideFieldError('confirmPassword');
        return true;
    }

    /**
     * 显示字段错误
     */
    showFieldError(fieldName, message) {
        const input = this.inputs[fieldName];
        const errorElement = document.getElementById(fieldName + 'Error');

        input.classList.add('error');
        if (errorElement) {
            errorElement.textContent = message;
            errorElement.style.display = 'block';
        }
    }

    /**
     * 隐藏字段错误
     */
    hideFieldError(fieldName) {
        const input = this.inputs[fieldName];
        const errorElement = document.getElementById(fieldName + 'Error');

        input.classList.remove('error');
        if (errorElement) {
            errorElement.style.display = 'none';
        }
    }

    /**
     * 获取表单数据
     */
    getFormData() {
        return {
            username: this.inputs.username.value.trim(),
            password: this.inputs.password.value,
            confirmPassword: this.inputs.confirmPassword.value
        };
    }

    /**
     * 提交注册请求
     */
    async submitRegistration(formData) {
        // 模拟API调用
        return new Promise((resolve) => {
            setTimeout(() => {
                // 模拟成功注册
                resolve({
                    success: true,
                    message: '注册成功'
                });
            }, 2000);
        });
    }



    /**
     * 设置提交按钮加载状态
     */
    setSubmitLoading(loading) {
        const button = this.buttons.register;
        const btnText = button.querySelector('.btn-text');
        const btnLoading = button.querySelector('.btn-loading');

        if (loading) {
            button.disabled = true;
            btnText.classList.add('hidden');
            btnLoading.classList.remove('hidden');
        } else {
            button.disabled = false;
            btnText.classList.remove('hidden');
            btnLoading.classList.add('hidden');
        }
    }

    /**
     * 显示提示消息
     */
    showToast(message, type = 'info') {
        // 创建toast元素
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        toast.textContent = message;

        // 添加到容器
        const container = document.getElementById('toastContainer');
        container.appendChild(toast);

        // 自动移除
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 3000);
    }

    /**
     * 销毁组件
     */
    destroy() {
        // 清理资源
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    window.registerPage = new RegisterPage();
});

// 页面卸载时清理
window.addEventListener('beforeunload', () => {
    if (window.registerPage) {
        window.registerPage.destroy();
    }
});
