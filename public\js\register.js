/**
 * 注册页面脚本
 * 处理用户注册相关功能
 */

class RegisterPage {
    constructor() {
        this.form = null;
        this.inputs = {};
        this.isSubmitting = false;
        this.countdownTimer = null;
        this.countdownSeconds = 60;

        this.init();
    }

    /**
     * 初始化页面
     */
    init() {
        this.bindElements();
        this.bindEvents();
        this.initValidation();
    }

    /**
     * 绑定DOM元素
     */
    bindElements() {
        this.form = document.getElementById('registerForm');
        this.inputs = {
            phone: document.getElementById('phone'),
            verificationCode: document.getElementById('verificationCode'),
            password: document.getElementById('password'),
            confirmPassword: document.getElementById('confirmPassword'),
            inviteCode: document.getElementById('inviteCode'),
            agreeTerms: document.getElementById('agreeTerms')
        };

        this.buttons = {
            register: document.getElementById('registerBtn'),
            sendCode: document.getElementById('sendCodeBtn'),
            passwordToggle: document.getElementById('passwordToggle'),
            confirmPasswordToggle: document.getElementById('confirmPasswordToggle')
        };

        this.elements = {
            strengthBar: document.querySelector('.strength-fill'),
            strengthText: document.querySelector('.strength-text'),
            countdown: document.querySelector('.countdown')
        };
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // 表单提交
        this.form.addEventListener('submit', (e) => this.handleSubmit(e));

        // 发送验证码
        this.buttons.sendCode.addEventListener('click', () => this.sendVerificationCode());

        // 密码显示/隐藏
        this.buttons.passwordToggle.addEventListener('click', () => this.togglePassword('password'));
        this.buttons.confirmPasswordToggle.addEventListener('click', () => this.togglePassword('confirmPassword'));

        // 实时验证
        this.inputs.phone.addEventListener('input', () => this.validatePhone());
        this.inputs.verificationCode.addEventListener('input', () => this.validateVerificationCode());
        this.inputs.password.addEventListener('input', () => this.validatePassword());
        this.inputs.confirmPassword.addEventListener('input', () => this.validateConfirmPassword());

        // 密码强度检测
        this.inputs.password.addEventListener('input', () => this.updatePasswordStrength());
    }

    /**
     * 初始化验证
     */
    initValidation() {
        // 手机号格式验证
        this.inputs.phone.addEventListener('blur', () => this.validatePhone());
        this.inputs.verificationCode.addEventListener('blur', () => this.validateVerificationCode());
        this.inputs.password.addEventListener('blur', () => this.validatePassword());
        this.inputs.confirmPassword.addEventListener('blur', () => this.validateConfirmPassword());
    }

    /**
     * 处理表单提交
     */
    async handleSubmit(e) {
        e.preventDefault();

        if (this.isSubmitting) return;

        // 验证所有字段
        const isValid = this.validateAll();
        if (!isValid) {
            this.showToast('请检查输入信息', 'error');
            return;
        }

        // 检查协议同意
        if (!this.inputs.agreeTerms.checked) {
            this.showToast('请先同意用户协议和隐私政策', 'warning');
            return;
        }

        this.isSubmitting = true;
        this.setSubmitLoading(true);

        try {
            const formData = this.getFormData();
            const result = await this.submitRegistration(formData);

            if (result.success) {
                this.showToast('注册成功！正在跳转...', 'success');
                setTimeout(() => {
                    window.location.href = 'login.html';
                }, 2000);
            } else {
                this.showToast(result.message || '注册失败，请重试', 'error');
            }
        } catch (error) {
            console.error('Registration error:', error);
            this.showToast('网络错误，请重试', 'error');
        } finally {
            this.isSubmitting = false;
            this.setSubmitLoading(false);
        }
    }

    /**
     * 发送验证码
     */
    async sendVerificationCode() {
        if (!this.validatePhone()) {
            this.showToast('请输入正确的手机号', 'error');
            return;
        }

        if (this.buttons.sendCode.disabled) return;

        try {
            const phone = this.inputs.phone.value.trim();
            const result = await this.requestVerificationCode(phone);

            if (result.success) {
                this.showToast('验证码已发送', 'success');
                this.startCountdown();
            } else {
                this.showToast(result.message || '发送失败，请重试', 'error');
            }
        } catch (error) {
            console.error('Send code error:', error);
            this.showToast('网络错误，请重试', 'error');
        }
    }

    /**
     * 开始倒计时
     */
    startCountdown() {
        this.buttons.sendCode.disabled = true;
        this.buttons.sendCode.querySelector('.btn-text').classList.add('hidden');
        this.elements.countdown.classList.remove('hidden');

        this.countdownSeconds = 60;
        this.updateCountdownDisplay();

        this.countdownTimer = setInterval(() => {
            this.countdownSeconds--;
            this.updateCountdownDisplay();

            if (this.countdownSeconds <= 0) {
                this.stopCountdown();
            }
        }, 1000);
    }

    /**
     * 停止倒计时
     */
    stopCountdown() {
        if (this.countdownTimer) {
            clearInterval(this.countdownTimer);
            this.countdownTimer = null;
        }

        this.buttons.sendCode.disabled = false;
        this.buttons.sendCode.querySelector('.btn-text').classList.remove('hidden');
        this.elements.countdown.classList.add('hidden');
    }

    /**
     * 更新倒计时显示
     */
    updateCountdownDisplay() {
        this.elements.countdown.textContent = `${this.countdownSeconds}s`;
    }

    /**
     * 切换密码显示
     */
    togglePassword(fieldName) {
        const input = this.inputs[fieldName];
        const button = this.buttons[fieldName + 'Toggle'];
        const icon = button.querySelector('i');

        if (input.type === 'password') {
            input.type = 'text';
            icon.className = 'fas fa-eye-slash';
        } else {
            input.type = 'password';
            icon.className = 'fas fa-eye';
        }
    }

    /**
     * 更新密码强度
     */
    updatePasswordStrength() {
        const password = this.inputs.password.value;
        const strength = this.calculatePasswordStrength(password);

        this.elements.strengthBar.className = 'strength-fill';
        
        if (strength.level === 'weak') {
            this.elements.strengthBar.classList.add('weak');
            this.elements.strengthText.textContent = '密码强度：弱';
        } else if (strength.level === 'medium') {
            this.elements.strengthBar.classList.add('medium');
            this.elements.strengthText.textContent = '密码强度：中';
        } else if (strength.level === 'strong') {
            this.elements.strengthBar.classList.add('strong');
            this.elements.strengthText.textContent = '密码强度：强';
        } else {
            this.elements.strengthText.textContent = '密码强度：弱';
        }
    }

    /**
     * 计算密码强度
     */
    calculatePasswordStrength(password) {
        if (!password) return { level: 'weak', score: 0 };

        let score = 0;
        
        // 长度检查
        if (password.length >= 8) score += 2;
        else if (password.length >= 6) score += 1;

        // 包含数字
        if (/\d/.test(password)) score += 1;

        // 包含小写字母
        if (/[a-z]/.test(password)) score += 1;

        // 包含大写字母
        if (/[A-Z]/.test(password)) score += 1;

        // 包含特殊字符
        if (/[^a-zA-Z0-9]/.test(password)) score += 1;

        if (score >= 5) return { level: 'strong', score };
        if (score >= 3) return { level: 'medium', score };
        return { level: 'weak', score };
    }

    /**
     * 验证所有字段
     */
    validateAll() {
        const validations = [
            this.validatePhone(),
            this.validateVerificationCode(),
            this.validatePassword(),
            this.validateConfirmPassword()
        ];

        return validations.every(result => result);
    }

    /**
     * 验证手机号
     */
    validatePhone() {
        const phone = this.inputs.phone.value.trim();
        const phoneRegex = /^1[3-9]\d{9}$/;
        
        if (!phone) {
            this.showFieldError('phone', '请输入手机号');
            return false;
        }
        
        if (!phoneRegex.test(phone)) {
            this.showFieldError('phone', '请输入正确的手机号');
            return false;
        }

        this.hideFieldError('phone');
        return true;
    }

    /**
     * 验证验证码
     */
    validateVerificationCode() {
        const code = this.inputs.verificationCode.value.trim();
        
        if (!code) {
            this.showFieldError('verificationCode', '请输入验证码');
            return false;
        }
        
        if (code.length !== 6) {
            this.showFieldError('verificationCode', '请输入6位验证码');
            return false;
        }

        this.hideFieldError('verificationCode');
        return true;
    }

    /**
     * 验证密码
     */
    validatePassword() {
        const password = this.inputs.password.value;
        
        if (!password) {
            this.showFieldError('password', '请输入密码');
            return false;
        }
        
        if (password.length < 6 || password.length > 20) {
            this.showFieldError('password', '密码长度为6-20位');
            return false;
        }

        this.hideFieldError('password');
        return true;
    }

    /**
     * 验证确认密码
     */
    validateConfirmPassword() {
        const password = this.inputs.password.value;
        const confirmPassword = this.inputs.confirmPassword.value;
        
        if (!confirmPassword) {
            this.showFieldError('confirmPassword', '请确认密码');
            return false;
        }
        
        if (password !== confirmPassword) {
            this.showFieldError('confirmPassword', '两次密码输入不一致');
            return false;
        }

        this.hideFieldError('confirmPassword');
        return true;
    }

    /**
     * 显示字段错误
     */
    showFieldError(fieldName, message) {
        const input = this.inputs[fieldName];
        const errorElement = document.getElementById(fieldName + 'Error');
        
        input.classList.add('error');
        if (errorElement) {
            errorElement.textContent = message;
            errorElement.style.display = 'block';
        }
    }

    /**
     * 隐藏字段错误
     */
    hideFieldError(fieldName) {
        const input = this.inputs[fieldName];
        const errorElement = document.getElementById(fieldName + 'Error');
        
        input.classList.remove('error');
        if (errorElement) {
            errorElement.style.display = 'none';
        }
    }

    /**
     * 获取表单数据
     */
    getFormData() {
        return {
            phone: this.inputs.phone.value.trim(),
            verificationCode: this.inputs.verificationCode.value.trim(),
            password: this.inputs.password.value,
            inviteCode: this.inputs.inviteCode.value.trim()
        };
    }

    /**
     * 提交注册请求
     */
    async submitRegistration(formData) {
        // 模拟API调用
        return new Promise((resolve) => {
            setTimeout(() => {
                // 模拟成功注册
                resolve({
                    success: true,
                    message: '注册成功'
                });
            }, 2000);
        });
    }

    /**
     * 请求验证码
     */
    async requestVerificationCode(phone) {
        // 模拟API调用
        return new Promise((resolve) => {
            setTimeout(() => {
                resolve({
                    success: true,
                    message: '验证码已发送'
                });
            }, 1000);
        });
    }

    /**
     * 设置提交按钮加载状态
     */
    setSubmitLoading(loading) {
        const button = this.buttons.register;
        const btnText = button.querySelector('.btn-text');
        const btnLoading = button.querySelector('.btn-loading');

        if (loading) {
            button.disabled = true;
            btnText.classList.add('hidden');
            btnLoading.classList.remove('hidden');
        } else {
            button.disabled = false;
            btnText.classList.remove('hidden');
            btnLoading.classList.add('hidden');
        }
    }

    /**
     * 显示提示消息
     */
    showToast(message, type = 'info') {
        // 创建toast元素
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        toast.textContent = message;

        // 添加到容器
        const container = document.getElementById('toastContainer');
        container.appendChild(toast);

        // 自动移除
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 3000);
    }

    /**
     * 销毁组件
     */
    destroy() {
        if (this.countdownTimer) {
            clearInterval(this.countdownTimer);
        }
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    window.registerPage = new RegisterPage();
});

// 页面卸载时清理
window.addEventListener('beforeunload', () => {
    if (window.registerPage) {
        window.registerPage.destroy();
    }
});
