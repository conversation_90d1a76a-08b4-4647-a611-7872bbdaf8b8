<?php
/**
 * 用户认证API接口
 * 处理登录、注册、Token验证等
 */

require_once '../config/database.php';
require_once '../utils/response.php';
require_once '../utils/auth.php';

class AuthAPI {
    
    /**
     * 处理用户登录
     */
    public function login() {
        Response::validateMethod(['POST']);
        
        $data = Response::getRequestData();
        Response::validateRequired($data, ['username', 'password']);
        
        $result = Auth::login($data['username'], $data['password']);
        
        if ($result['success']) {
            Response::success($result['data'], $result['message']);
        } else {
            Response::error($result['message'], 401);
        }
    }
    
    /**
     * 处理用户注册
     */
    public function register() {
        Response::validateMethod(['POST']);
        
        $data = Response::getRequestData();
        Response::validateRequired($data, ['username', 'password']);
        
        // 验证密码强度
        $passwordValidation = Auth::validatePasswordStrength($data['password']);
        if (!$passwordValidation['valid']) {
            Response::validationError($passwordValidation['errors']);
        }
        
        // 验证邮箱格式（如果提供）
        if (!empty($data['email']) && !filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
            Response::validationError(['email' => '邮箱格式不正确']);
        }
        
        // 验证手机号格式（如果提供）
        if (!empty($data['phone']) && !preg_match('/^1[3-9]\d{9}$/', $data['phone'])) {
            Response::validationError(['phone' => '手机号格式不正确']);
        }
        
        $result = Auth::register($data);
        
        if ($result['success']) {
            Response::success($result['data'], $result['message'], 201);
        } else {
            Response::error($result['message'], 400);
        }
    }
    
    /**
     * 验证Token
     */
    public function validate() {
        Response::validateMethod(['GET']);
        
        $token = Auth::getTokenFromRequest();
        $result = Auth::validateToken($token);
        
        if ($result['success']) {
            Response::success($result['data'], $result['message']);
        } else {
            Response::unauthorized($result['message']);
        }
    }
    
    /**
     * 用户登出
     */
    public function logout() {
        Response::validateMethod(['POST']);
        
        $token = Auth::getTokenFromRequest();
        $result = Auth::logout($token);
        
        if ($result['success']) {
            Response::success(null, $result['message']);
        } else {
            Response::error($result['message']);
        }
    }
    
    /**
     * 刷新Token
     */
    public function refresh() {
        Response::validateMethod(['POST']);
        
        $token = Auth::getTokenFromRequest();
        $result = Auth::refreshToken($token);
        
        if ($result['success']) {
            Response::success($result['data'], $result['message']);
        } else {
            Response::unauthorized($result['message']);
        }
    }
    
    /**
     * 获取当前用户信息
     */
    public function profile() {
        Response::validateMethod(['GET']);
        
        $user = Auth::requireAuth();
        
        // 获取用户详细信息
        $db = Database::getInstance();
        $userProfile = $db->fetchOne(
            "SELECT id, username, email, phone, nickname, avatar, status, created_at, updated_at 
             FROM users WHERE id = ?",
            [$user['user_id']]
        );
        
        if ($userProfile) {
            // 添加统计信息
            $userProfile['stats'] = [
                'login_count' => $this->getUserLoginCount($user['user_id']),
                'last_login' => $this->getUserLastLogin($user['user_id']),
                'account_age_days' => $this->getAccountAgeDays($userProfile['created_at'])
            ];
            
            Response::success($userProfile);
        } else {
            Response::notFound('用户信息不存在');
        }
    }
    
    /**
     * 更新用户信息
     */
    public function updateProfile() {
        Response::validateMethod(['PUT', 'POST']);
        
        $user = Auth::requireAuth();
        $data = Response::getRequestData();
        
        // 允许更新的字段
        $allowedFields = ['nickname', 'email', 'phone', 'avatar'];
        $updateData = [];
        
        foreach ($allowedFields as $field) {
            if (isset($data[$field])) {
                $updateData[$field] = $data[$field];
            }
        }
        
        if (empty($updateData)) {
            Response::error('没有可更新的数据');
        }
        
        // 验证邮箱格式
        if (isset($updateData['email']) && !filter_var($updateData['email'], FILTER_VALIDATE_EMAIL)) {
            Response::validationError(['email' => '邮箱格式不正确']);
        }
        
        // 验证手机号格式
        if (isset($updateData['phone']) && !preg_match('/^1[3-9]\d{9}$/', $updateData['phone'])) {
            Response::validationError(['phone' => '手机号格式不正确']);
        }
        
        $db = Database::getInstance();
        
        // 检查邮箱是否已被其他用户使用
        if (isset($updateData['email'])) {
            $existingEmail = $db->fetchOne(
                "SELECT id FROM users WHERE email = ? AND id != ?",
                [$updateData['email'], $user['user_id']]
            );
            
            if ($existingEmail) {
                Response::error('邮箱已被其他用户使用');
            }
        }
        
        // 检查手机号是否已被其他用户使用
        if (isset($updateData['phone'])) {
            $existingPhone = $db->fetchOne(
                "SELECT id FROM users WHERE phone = ? AND id != ?",
                [$updateData['phone'], $user['user_id']]
            );
            
            if ($existingPhone) {
                Response::error('手机号已被其他用户使用');
            }
        }
        
        // 更新用户信息
        $updateData['updated_at'] = date('Y-m-d H:i:s');
        $affected = $db->update('users', $updateData, 'id = ?', [$user['user_id']]);
        
        if ($affected > 0) {
            Response::success(null, '用户信息更新成功');
        } else {
            Response::error('用户信息更新失败');
        }
    }
    
    /**
     * 修改密码
     */
    public function changePassword() {
        Response::validateMethod(['POST']);
        
        $user = Auth::requireAuth();
        $data = Response::getRequestData();
        
        Response::validateRequired($data, ['current_password', 'new_password']);
        
        // 验证新密码强度
        $passwordValidation = Auth::validatePasswordStrength($data['new_password']);
        if (!$passwordValidation['valid']) {
            Response::validationError($passwordValidation['errors']);
        }
        
        $db = Database::getInstance();
        
        // 获取当前密码
        $currentUser = $db->fetchOne(
            "SELECT password FROM users WHERE id = ?",
            [$user['user_id']]
        );
        
        if (!$currentUser) {
            Response::notFound('用户不存在');
        }
        
        // 验证当前密码
        if (!password_verify($data['current_password'], $currentUser['password'])) {
            Response::error('当前密码错误', 400);
        }
        
        // 检查新密码是否与当前密码相同
        if (password_verify($data['new_password'], $currentUser['password'])) {
            Response::error('新密码不能与当前密码相同', 400);
        }
        
        // 更新密码
        $hashedPassword = password_hash($data['new_password'], PASSWORD_DEFAULT);
        $affected = $db->update('users', 
            [
                'password' => $hashedPassword,
                'updated_at' => date('Y-m-d H:i:s')
            ],
            'id = ?',
            [$user['user_id']]
        );
        
        if ($affected > 0) {
            // 清除所有会话，强制重新登录
            $db->delete('user_sessions', 'user_id = ?', [$user['user_id']]);
            
            Response::success(null, '密码修改成功，请重新登录');
        } else {
            Response::error('密码修改失败');
        }
    }
    
    /**
     * 获取用户登录次数
     */
    private function getUserLoginCount($userId) {
        // 这里可以从日志表中统计，暂时返回模拟数据
        return rand(10, 100);
    }
    
    /**
     * 获取用户最后登录时间
     */
    private function getUserLastLogin($userId) {
        $db = Database::getInstance();
        $lastSession = $db->fetchOne(
            "SELECT created_at FROM user_sessions WHERE user_id = ? ORDER BY created_at DESC LIMIT 1",
            [$userId]
        );
        
        return $lastSession ? $lastSession['created_at'] : null;
    }
    
    /**
     * 获取账户年龄（天数）
     */
    private function getAccountAgeDays($createdAt) {
        $created = new DateTime($createdAt);
        $now = new DateTime();
        return $now->diff($created)->days;
    }
}

// 处理请求
$api = new AuthAPI();
$action = $_GET['action'] ?? 'login';

switch ($action) {
    case 'login':
        $api->login();
        break;
    case 'register':
        $api->register();
        break;
    case 'validate':
        $api->validate();
        break;
    case 'logout':
        $api->logout();
        break;
    case 'refresh':
        $api->refresh();
        break;
    case 'profile':
        $api->profile();
        break;
    case 'update-profile':
        $api->updateProfile();
        break;
    case 'change-password':
        $api->changePassword();
        break;
    default:
        Response::error('无效的操作');
}
?>
