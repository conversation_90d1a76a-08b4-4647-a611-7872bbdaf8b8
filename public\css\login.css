/* 登录页面专用样式 */

.login-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 20px;
    position: relative;
}

.back-btn {
    position: absolute;
    top: 40px;
    left: 20px;
    background: rgba(255,255,255,0.2);
    border: none;
    color: white;
    padding: 12px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 18px;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    z-index: 10;
}

.back-btn:hover {
    background: rgba(255,255,255,0.3);
    transform: translateX(-2px);
}

.login-box {
    background: white;
    border-radius: 16px;
    padding: 40px 30px;
    width: 100%;
    max-width: 400px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    backdrop-filter: blur(10px);
    animation: slideUp 0.5s ease-out;
}

.login-header {
    text-align: center;
    margin-bottom: 40px;
}

.login-logo {
    width: 60px;
    height: 60px;
    background: linear-gradient(45deg, #1e88e5, #e53935);
    border-radius: 12px;
    margin: 0 auto 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 24px;
    box-shadow: 0 8px 16px rgba(30, 136, 229, 0.3);
}

.login-title {
    font-size: 24px;
    font-weight: bold;
    color: #333;
    margin-bottom: 8px;
}

.login-subtitle {
    color: #666;
    font-size: 14px;
}

.form-group {
    margin-bottom: 20px;
}

.form-label {
    display: block;
    margin-bottom: 8px;
    color: #333;
    font-size: 14px;
    font-weight: 500;
}

.form-input {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 16px;
    transition: all 0.3s ease;
    background-color: #fff;
}

.form-input:focus {
    outline: none;
    border-color: #1976d2;
    box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.1);
}

.form-input.error {
    border-color: #f44336;
    box-shadow: 0 0 0 3px rgba(244, 67, 54, 0.1);
}

.password-input-wrapper {
    position: relative;
}

.password-toggle {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #666;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: color 0.3s ease;
}

.password-toggle:hover {
    color: #1976d2;
}

.error-message {
    color: #f44336;
    font-size: 12px;
    margin-top: 4px;
    display: none;
    animation: fadeIn 0.3s ease;
}

.login-options {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin-bottom: 30px;
}

.remember-me {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: #666;
    cursor: pointer;
    position: relative;
}

.remember-me input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 18px;
    height: 18px;
    border: 2px solid #ddd;
    border-radius: 4px;
    position: relative;
    transition: all 0.3s ease;
}

.remember-me input[type="checkbox"]:checked + .checkmark {
    background-color: #1976d2;
    border-color: #1976d2;
}

.remember-me input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}



.login-btn {
    width: 100%;
    padding: 14px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: 20px;
    position: relative;
    overflow: hidden;
}

.login-btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 8px 16px rgba(102, 126, 234, 0.3);
}

.login-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.btn-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}



.register-link {
    text-align: center;
    color: #666;
    font-size: 14px;
}

.register-link a {
    color: #1976d2;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

.register-link a:hover {
    color: #1565c0;
    text-decoration: underline;
}

/* Toast 样式 */
.toast {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    padding: 12px 24px;
    border-radius: 8px;
    color: white;
    font-size: 14px;
    font-weight: 500;
    z-index: 1000;
    box-shadow: 0 4px 16px rgba(0,0,0,0.15);
    backdrop-filter: blur(10px);
    animation: slideDown 0.3s ease;
}

.toast.success {
    background-color: rgba(76, 175, 80, 0.9);
}

.toast.error {
    background-color: rgba(244, 67, 54, 0.9);
}

.toast.warning {
    background-color: rgba(255, 152, 0, 0.9);
}

.toast.info {
    background-color: rgba(33, 150, 243, 0.9);
}

/* 工具类 */
.hidden {
    display: none !important;
}

.visible {
    display: block !important;
}

/* 动画 */
@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateX(-50%) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

/* 响应式设计 */
@media (max-width: 480px) {
    .login-container {
        padding: 16px;
    }

    .login-box {
        padding: 30px 20px;
    }

    .login-title {
        font-size: 20px;
    }

    .back-btn {
        top: 20px;
        left: 16px;
    }
}

/* 注册页面特定样式 */



@media (max-width: 360px) {
    .login-box {
        padding: 24px 16px;
    }

    .form-input {
        font-size: 14px;
    }

    .login-btn {
        font-size: 14px;
    }
}
